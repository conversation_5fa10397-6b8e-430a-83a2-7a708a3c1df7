package org.seven.share.common.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: AuthBody
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/1
 */
@Data
public class AuthBody implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotEmpty
    private String usertoken;

    @NotEmpty
    private String carid;

    @NotEmpty
    private String nodeType;

    private Integer planType;
}
