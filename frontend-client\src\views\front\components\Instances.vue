<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick, watch } from 'vue'
import { useSiteStore } from '@/store/modules/site';
import { useUserStore } from '@/store/modules/user';
import * as api from '@/api/user.js';
import { getClaudeList, authCar } from '@/api/claude.js';
import { getToken } from '@/utils/auth';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import Cookies from 'js-cookie';
import Image from './Image.vue'; // 导入Image组件

const router = useRouter();
// Initialize i18n
const { t } = useI18n();
const isLogin = computed(() => (getToken() ? true : false));
const siteStore = useSiteStore();
const userStore = useUserStore();
const selectLoading = ref(false);
const nodeType = ref();
const userName = computed(() => userStore.username);

// 导入本地logo图片
import chatgptLogo from '@/assets/chatgpt.png'
import claudeLogo from '@/assets/claude.png'
import grokLogo from '@/assets/grok.png'
import drawLogo from '@/assets/draw.jpg'
import drawDarkLogo from '@/assets/draw-dark.png'
// 获取 AI 模型 logo 路径
const getModelLogo = (type) => {
  const logos = {
    'free': chatgptLogo,
    '4o': chatgptLogo,
    'plus': chatgptLogo,
    'claude': claudeLogo,
    'grok': grokLogo,
    'draw': drawLogo
  }
  return logos[type] || chatgptLogo
}

// 获取模型logo的CSS类名（处理暗黑模式）
const getModelLogoClass = (type, isActive = false, isInstanceCard = false) => {
  const baseClasses = 'w-full h-full object-contain transition-all duration-300';

  // 根据不同模型类型处理暗黑模式
  switch (type) {
    case 'grok':
      if (isActive) {
        // 选中状态：明亮模式下变黑，暗黑模式下变白
        return `${baseClasses} brightness-0 dark:invert contrast-200`;
      } else if (isInstanceCard) {
        // 实例卡片中的Grok logo：增强显示效果
        return `${baseClasses} filter dark:invert invert-0 brightness-110 contrast-125`;
      } else {
        // 未选中状态：适配暗黑模式
        return `${baseClasses} filter dark:invert invert-0 opacity-80 group-hover:opacity-100`;
      }

    case 'claude':
      // Claude logo在暗黑模式下需要反色
      if (isInstanceCard) {
        return `${baseClasses} filter dark:invert invert-0 brightness-105 contrast-110`;
      }
      return `${baseClasses} filter dark:invert invert-0 group-hover:scale-110`;

    case 'draw':
      // 绘图logo在暗黑模式下需要反色
      if (isInstanceCard) {
        return `${baseClasses} filter dark:invert invert-0 brightness-105 contrast-110`;
      }
      return `${baseClasses} filter dark:invert invert-0 group-hover:scale-110`;

    case 'free':
    case '4o':
    case 'plus':
    default:
      // ChatGPT logo在暗黑模式下需要反色
      if (isInstanceCard) {
        return `${baseClasses} filter dark:invert invert-0 brightness-105 contrast-110`;
      }
      return `${baseClasses} filter dark:invert invert-0 group-hover:scale-110`;
  }
}

// 获取账号类型显示文本
const getAccountType = (type, isPlus) => {
  if (type === 'claude') {
    if (isPlus === 1) return t('instances.types.pro')
    if (isPlus === 2) return t('instances.types.max')
    return t('instances.types.claude37')
  }

  if (type === 'gpt' || type === '4o' || type === 'plus') {
    if (type === '4o') return t('instances.types.4o')
    if (type === 'plus' && isPlus === 2) return t('instances.types.team')
    if (isPlus === 3) return t('instances.types.pro')
    if (type === 'plus') return t('instances.types.plus')
    if (type === 'free') return t('instances.types.free')
    return type.charAt(0).toUpperCase() + type.slice(1)
  }

  if (type === 'grok') { 
    if (isPlus == 0) {
      return t('instances.models.grok')
    } else { 
      return t('instances.models.grokSuper')
    }
  } 

  return type ? type.charAt(0).toUpperCase() + type.slice(1) : t('instances.types.standard')
}

const showIframe = ref(false);
const iframeUrl = ref('');
const loading = ref(false)
const AIModels = ref([])
const activeModel = ref('ChatGPT Plus')
const carIds = ref([]);
const instances = ref([])
const showMobileMenu = ref(false)
const showUserMenu = ref(false)
const timer = ref(null);
// 绘图组件相关状态
const showDrawPanel = ref(false);
// 修改数据结构，为每种类型存储实例数据
const instancesData = ref({
  free: [],
  '4o': [],
  plus: [],
  claude: [],
})

// 用户选择习惯缓存相关
const USER_PREFERENCE_KEY = 'user_model_preference';
const CACHE_EXPIRY_DAYS = 30; // 缓存30天

// 获取用户偏好缓存
const getUserPreference = () => {
  try {
    const cached = localStorage.getItem(USER_PREFERENCE_KEY);
    if (!cached) return null;
    
    const data = JSON.parse(cached);
    
    // 检查缓存是否过期
    if (data.timestamp && (Date.now() - data.timestamp) > (CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000)) {
      localStorage.removeItem(USER_PREFERENCE_KEY);
      return null;
    }
    
    return data.preference;
  } catch (error) {
    console.warn('读取用户偏好缓存失败:', error);
    localStorage.removeItem(USER_PREFERENCE_KEY);
    return null;
  }
};

// 安全地应用用户偏好
const applyUserPreference = (validNodes) => {
  try {
    const userPreference = getUserPreference();
    
    if (userPreference && userPreference.modelName && userPreference.modelType) {
      // 查找用户偏好的模型是否在当前可用节点中
      const preferredNode = validNodes.find(node => 
        node.name === userPreference.modelName && node.type === userPreference.modelType
      );
      
      if (preferredNode) {
        return preferredNode;
      } else {
        console.log('用户偏好模型不可用，使用默认模型');
        // 清除无效的偏好缓存
        clearUserPreference();
      }
    }
    
    return validNodes[0]; // 返回第一个节点作为默认
  } catch (error) {
    console.warn('应用用户偏好时出错:', error);
    return validNodes[0]; // 出错时返回第一个节点
  }
};

// 保存用户偏好缓存
const saveUserPreference = (modelName, modelType) => {
  try {
    const preference = {
      modelName,
      modelType,
      timestamp: Date.now()
    };
    
    localStorage.setItem(USER_PREFERENCE_KEY, JSON.stringify({
      preference,
      timestamp: Date.now()
    }));
  } catch (error) {
    console.warn('保存用户偏好缓存失败:', error);
  }
};

// 清除用户偏好缓存
const clearUserPreference = () => {
  try {
    localStorage.removeItem(USER_PREFERENCE_KEY);
  } catch (error) {
    console.warn('清除用户偏好缓存失败:', error);
  }
};

// AI服务状态监控
const openaiStatus = ref(null);
const claudeStatus = ref(null);
const statusLoading = ref(false);
const statusTimer = ref(null);

// 公告系统 - 从siteStore动态获取HTML内容
const announcements = computed(() => ({
  claude: {
    content: siteStore.claudeAnnouncement || '', // Claude使用通用公告
    type: 'info'
  },
  grok: {
    content: siteStore.grokAnnouncement || '', // Grok使用通用公告
    type: 'info'
  },
  draw: {
    content: siteStore.drawAnnouncement || '', // Draw优先使用专属公告，回退到通用公告
    type: 'success'
  },
  gpt: {
    content: siteStore.gptAnnouncement || '', // GPT系列使用通用公告
    type: 'info'
  }
}));

// CSS安全处理函数
const sanitizeCSS = (cssText) => {
  if (!cssText) return '';

  // 移除危险的CSS属性和值
  const dangerousPatterns = [
    // 移除 @import 规则
    /@import\s+[^;]+;?/gi,
    // 移除 javascript: 协议
    /javascript\s*:/gi,
    // 移除 expression() 函数
    /expression\s*\([^)]*\)/gi,
    // 移除 behavior 属性
    /behavior\s*:[^;]+;?/gi,
    // 移除 -moz-binding 属性
    /-moz-binding\s*:[^;]+;?/gi,
    // 移除 @media 规则中的危险内容
    /@media[^{]*\{[^}]*\}/gi,
    // 移除 @keyframes 规则
    /@keyframes[^{]*\{[^}]*\}/gi,
    // 移除 position: fixed 和 position: absolute 的全局定位
    /position\s*:\s*(fixed|absolute)\s*;?/gi,
    // 移除 z-index 过高的值
    /z-index\s*:\s*\d{4,}\s*;?/gi
  ];

  let sanitized = cssText;
  dangerousPatterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });

  return sanitized;
};

// JavaScript安全处理函数
const sanitizeJS = (jsText) => {
  if (!jsText) return '';

  // 移除危险的JavaScript代码模式
  const dangerousPatterns = [
    // 移除 eval() 函数调用
    /eval\s*\([^)]*\)/gi,
    // 移除 Function() 构造函数
    /new\s+Function\s*\([^)]*\)/gi,
    // 移除 setTimeout/setInterval 的字符串参数形式
    /setTimeout\s*\(\s*["'][^"']*["']/gi,
    /setInterval\s*\(\s*["'][^"']*["']/gi,
    // 移除 document.write
    /document\.write/gi,
    // 移除 innerHTML 的直接赋值（防止XSS）
    /\.innerHTML\s*=/gi,
    // 移除 outerHTML 的直接赋值
    /\.outerHTML\s*=/gi,
    // 移除 location 相关的跳转
    /location\.(href|replace|assign)/gi,
    // 移除 window.open
    /window\.open/gi,
    // 移除 XMLHttpRequest 和 fetch（防止跨域请求）
    /new\s+XMLHttpRequest/gi,
    /fetch\s*\(/gi,
    // 移除 localStorage/sessionStorage 访问
    /(localStorage|sessionStorage)\./gi,
    // 移除 cookie 访问
    /document\.cookie/gi
  ];

  let sanitized = jsText;
  dangerousPatterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '/* 已移除危险代码 */');
  });

  return sanitized;
};

// HTML内容安全处理函数
const sanitizeHTML = (htmlContent) => {
  if (!htmlContent) return '';

  // 创建临时DOM元素来解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  // 移除所有 <style> 标签
  const styleTags = tempDiv.querySelectorAll('style');
  styleTags.forEach(tag => tag.remove());

  // 移除所有 <link> 标签（可能引入外部CSS）
  const linkTags = tempDiv.querySelectorAll('link');
  linkTags.forEach(tag => tag.remove());

  // 移除所有 <script> 标签
  const scriptTags = tempDiv.querySelectorAll('script');
  scriptTags.forEach(tag => tag.remove());

  // 移除所有元素的 style 属性
  const allElements = tempDiv.querySelectorAll('*');
  allElements.forEach(element => {
    element.removeAttribute('style');
    element.removeAttribute('onload');
    element.removeAttribute('onerror');
    element.removeAttribute('onclick');
    element.removeAttribute('onmouseover');
    element.removeAttribute('onmouseout');
  });

  return tempDiv.innerHTML;
};

// 获取当前模型的公告
const getCurrentAnnouncement = computed(() => {
  if (!nodeType.value) return null;

  let announcement = null;

  // Claude 模型显示 Claude 公告
  if (nodeType.value === 'claude') {
    announcement = announcements.value.claude;
  }
  // Grok 模型显示 Grok 公告
  else if (nodeType.value === 'grok') {
    announcement = announcements.value.grok;
  }
  // Draw 模型显示 Draw 公告
  else if (nodeType.value === 'draw') {
    announcement = announcements.value.draw;
  }
  // 其他所有模型（free, 4o, plus, embedded, sass等）显示 GPT 公告
  else {
    announcement = announcements.value.gpt;
  }

  // 只有当公告内容不为空时才返回公告对象
  if (!announcement || !announcement.content || announcement.content.trim() === '') {
    return null;
  }

  return announcement.content;
});

// 移除了复杂的CSS沙盒逻辑，现在使用iframe完全隔离

// 公告折叠状态管理
const announcementIframe = ref(null);
const isAnnouncementExpanded = ref(false);
const isAnnouncementLoading = ref(false);
const announcementContentCache = ref('');

// 滑动指示器状态
const modelScrollContainer = ref(null);
const desktopModelScrollContainer = ref(null);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);



// 切换公告展开/折叠状态 - 优化版本
const toggleAnnouncement = () => {
  if (!isAnnouncementExpanded.value) {
    // 展开时预处理内容
    isAnnouncementLoading.value = true;

    // 预缓存内容，避免展开时的处理延迟
    if (!announcementContentCache.value) {
      announcementContentCache.value = getOptimizedAnnouncementContent();
    }

    // 立即展开，不等待iframe加载
    isAnnouncementExpanded.value = true;

    // 短暂延迟后停止loading状态
    setTimeout(() => {
      isAnnouncementLoading.value = false;
    }, 100);
  } else {
    // 折叠时立即响应
    isAnnouncementExpanded.value = false;
    isAnnouncementLoading.value = false;
  }
};



const handleAnnouncementIframeLoad = () => {
  // 简化iframe加载处理，减少延迟
  const iframe = announcementIframe.value;
  if (iframe) {
    // 使用requestAnimationFrame优化性能
    requestAnimationFrame(() => {
      try {
        // 快速高度估算，避免复杂计算
        const content = getCurrentAnnouncement.value;
        let estimatedHeight = 200; // 默认高度

        if (content) {
          // 基于内容长度的快速估算
          const textLength = content.replace(/<[^>]*>/g, '').length; // 移除HTML标签
          estimatedHeight = Math.max(120, Math.min(textLength / 6 + 100, 350));
        }

        iframe.style.height = estimatedHeight + 'px';

        // 异步尝试精确高度调整（不阻塞UI）
        setTimeout(() => {
          try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (iframeDoc && iframeDoc.body) {
              const actualHeight = Math.max(
                iframeDoc.body.scrollHeight,
                iframeDoc.body.offsetHeight
              );

              if (actualHeight > 0 && actualHeight !== estimatedHeight) {
                const optimalHeight = Math.max(120, Math.min(actualHeight + 20, 350));
                iframe.style.height = optimalHeight + 'px';
              }
            }
          } catch (error) {
            // 跨域时忽略精确调整
          }
        }, 50); // 50ms后进行精确调整

      } catch (error) {
        // 出错时使用固定高度
        iframe.style.height = '200px';
      }
    });
  }
};

// 优化公告内容，添加紧凑样式和缓存
const getOptimizedAnnouncementContent = () => {
  const content = getCurrentAnnouncement.value;
  if (!content) return '';

  // 检查缓存
  if (announcementContentCache.value && announcementContentCache.value.includes(content)) {
    return announcementContentCache.value;
  }

  // 为公告内容添加紧凑的CSS样式
  const optimizedContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
          font-size: 13px;
          line-height: 1.4;
          color: #374151;
          background: transparent;
          padding: 8px 0;
          overflow-wrap: break-word;
          word-wrap: break-word;
        }

        /* 标题样式 - 更紧凑 */
        h1, h2, h3, h4, h5, h6 {
          font-weight: 600;
          margin: 4px 0 2px 0;
          line-height: 1.3;
          color: #111827;
        }

        h1 { font-size: 16px; }
        h2 { font-size: 15px; }
        h3 { font-size: 14px; }
        h4 { font-size: 13px; }

        /* 段落样式 - 减少间距 */
        p {
          margin: 2px 0;
          line-height: 1.4;
        }

        /* 列表样式 - 紧凑 */
        ul, ol {
          margin: 2px 0;
          padding-left: 16px;
        }

        li {
          margin: 1px 0;
          line-height: 1.3;
        }

        /* 链接样式 */
        a {
          color: #111827;
          text-decoration: underline;
          text-underline-offset: 1px;
        }

        /* 强调文本 */
        strong, b {
          font-weight: 600;
          color: #111827;
        }

        /* 代码样式 - 紧凑 */
        code {
          background: #f3f4f6;
          color: #111827;
          padding: 1px 4px;
          border-radius: 3px;
          font-family: 'SF Mono', 'Monaco', monospace;
          font-size: 12px;
        }

        /* 预格式化文本 - 紧凑 */
        pre {
          background: #f3f4f6;
          color: #111827;
          padding: 8px;
          border-radius: 4px;
          overflow-x: auto;
          margin: 4px 0;
          font-family: 'SF Mono', 'Monaco', monospace;
          font-size: 12px;
          line-height: 1.3;
        }

        /* 引用样式 - 紧凑 */
        blockquote {
          border-left: 2px solid #111827;
          padding-left: 8px;
          margin: 4px 0;
          font-style: italic;
          color: #6b7280;
        }

        /* 分割线 */
        hr {
          border: none;
          border-top: 1px solid #e5e7eb;
          margin: 6px 0;
        }

        /* 图片样式 */
        img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          margin: 2px 0;
        }

        /* 表格样式 - 紧凑 */
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 4px 0;
          font-size: 12px;
        }

        th, td {
          padding: 4px 6px;
          text-align: left;
          border-bottom: 1px solid #e5e7eb;
        }

        th {
          background: #f9fafb;
          font-weight: 600;
          color: #111827;
        }

        /* 暗黑模式适配 */
        @media (prefers-color-scheme: dark) {
          body {
            color: #d1d5db;
          }

          h1, h2, h3, h4, h5, h6,
          strong, b, a {
            color: #f9fafb;
          }

          code, pre {
            background: #374151;
            color: #f9fafb;
          }

          blockquote {
            border-left-color: #f9fafb;
            color: #9ca3af;
          }

          hr {
            border-top-color: #4b5563;
          }

          th {
            background: #374151;
            color: #f9fafb;
          }

          th, td {
            border-bottom-color: #4b5563;
          }
        }

        /* 移除多余的空白 */
        :first-child {
          margin-top: 0 !important;
        }

        :last-child {
          margin-bottom: 0 !important;
        }
      </style>
    </head>
    <body>
      ${content}
    </body>
    </html>
  `;

  // 缓存优化后的内容
  announcementContentCache.value = optimizedContent;

  return optimizedContent;
};

// 监听公告内容变化，预加载内容
watch(() => getCurrentAnnouncement.value, (newContent) => {
  if (newContent) {
    // 预缓存内容，提升展开性能
    nextTick(() => {
      announcementContentCache.value = getOptimizedAnnouncementContent();
    });
  } else {
    // 清空缓存
    announcementContentCache.value = '';
  }
}, { immediate: true });

// 滑动控制方法
const scrollLeft = () => {
  // 根据当前屏幕宽度选择对应的容器
  const mobileContainer = modelScrollContainer.value;
  const desktopContainer = desktopModelScrollContainer.value;
  const container = window.innerWidth >= 768 ? desktopContainer : mobileContainer;
  
  if (container) {
    const scrollAmount = container.clientWidth * 0.8; // 滚动80%的可视宽度
    container.scrollBy({
      left: -scrollAmount,
      behavior: 'smooth'
    });
  }
};

const scrollRight = () => {
  // 根据当前屏幕宽度选择对应的容器
  const mobileContainer = modelScrollContainer.value;
  const desktopContainer = desktopModelScrollContainer.value;
  const container = window.innerWidth >= 768 ? desktopContainer : mobileContainer;
  
  if (container) {
    const scrollAmount = container.clientWidth * 0.8; // 滚动80%的可视宽度
    container.scrollBy({
      left: scrollAmount,
      behavior: 'smooth'
    });
  }
};

// 处理滚动事件，更新箭头显示状态
const handleScroll = () => {
  // 根据当前屏幕宽度选择对应的容器
  const mobileContainer = modelScrollContainer.value;
  const desktopContainer = desktopModelScrollContainer.value;
  const currentContainer = window.innerWidth >= 768 ? desktopContainer : mobileContainer;
  
  if (currentContainer) {
    const { scrollLeft, scrollWidth, clientWidth } = currentContainer;

    // 左箭头：当不在最左侧时显示
    showLeftArrow.value = scrollLeft > 10;

    // 右箭头：当不在最右侧时显示
    showRightArrow.value = scrollLeft < scrollWidth - clientWidth - 10;
  }
};

// 检查是否需要显示滑动指示器
const checkScrollIndicators = () => {
  nextTick(() => {
    // 分别检查手机端和桌面端容器
    const mobileContainer = modelScrollContainer.value;
    const desktopContainer = desktopModelScrollContainer.value;
    
    // 优先检查当前可见的容器
    const currentContainer = window.innerWidth >= 768 ? desktopContainer : mobileContainer;
    
    if (currentContainer) {
      const { scrollWidth, clientWidth } = currentContainer;
      const needsScroll = scrollWidth > clientWidth;

      if (needsScroll) {
        // 初始状态：左箭头隐藏，右箭头显示
        showLeftArrow.value = false;
        showRightArrow.value = true;
        // 需要滚动时移除居中样式
        currentContainer.classList.remove('justify-center');
        currentContainer.classList.add('justify-start');
      } else {
        // 不需要滚动时隐藏所有箭头
        showLeftArrow.value = false;
        showRightArrow.value = false;
        // 不需要滚动时添加居中样式
        currentContainer.classList.remove('justify-start');
        currentContainer.classList.add('justify-center');
      }
    }
  });
};

// 修改 handleModelClick 函数
const handleModelClick = async (model) => {
  // 安全检查
  if (!model || !model.name || !model.type) {
    return;
  }

  instances.value = [];
  activeModel.value = model.name
  nodeType.value = model.type

  // 保存用户选择偏好到缓存
  saveUserPreference(model.name, model.type);

  // 重置绘图面板状态
  showDrawPanel.value = false;

  // 异步检查模型类型并获取相应状态
  setupStatusMonitoring(model.name);

  // 处理 grok 类型
  if (model.type === 'grok') {
    showIframe.value = false;
    // 加载 grok 数据
    await loadTypeData('grok');
    return;
  }

  // 处理 embedded 类型
  if (model.type === 'embedded') {
    await api.checkAccess({ userId: userStore.id });

    // 获取跳转方式配置
    const openType = model.openType;
    const targetUrl = model.url ? model.url : siteStore.backupUrl;
    // 根据openType决定跳转方式
    if (openType === '_blank') {
      // 新窗口跳转
      window.open(targetUrl, '_blank');
      return;
    } else {
      // _self 或空值使用iframe
      showIframe.value = false;
      await nextTick(() => {
        iframeUrl.value = targetUrl;
        showIframe.value = true;
      });
      return;
    }
  }

  // 处理绘图类型
  if (model.type === 'draw') {
    showIframe.value = false;
    // 加载绘图数据或跳转到绘图页面
    await loadTypeData('draw');
    return;
  }

  // 处理其他类型
  showIframe.value = false;
  await loadTypeData(model.type);
};

// 修改 loadTypeData 函数中的 grok 部分
const loadTypeData = async (type) => {
  loading.value = true;
  try {
    let result;
    switch (type) {
      case 'free':
      case '4o':
      case 'plus':
        result = await api.getCarList({ type, page: 1, size: 1000 });
        if (result?.carList) {
          instancesData.value[type] = Array.isArray(carIds.value) && carIds.value.length > 0
            ? result.carList.filter(e => carIds.value.includes(String(e.id)))
            : result.carList;
        }
        break;
      case 'claude':
        result = await getClaudeList();
        instancesData.value[type] = result || [];
        break;
      case 'grok':
        result = await api.fetchGrokList()
        instancesData.value[type] = result || [];
        break;
      case 'sass':
        result = await api.fetchSassList();
        instancesData.value[type] = result || [];
        break;
      case 'draw':
        // 获取绘图相关数据，如果需要的话，或者创建虚拟示例
        if (!instancesData.value[type]) {
          instancesData.value[type] = [{
            carID: t('instances.models.draw'),
            status: t('status.idle'),
            count: 0,
            detail: t('instances.drawPanel.title'),
            isPlus: 0
          }];
        }
        // 显示绘图面板
        showDrawPanel.value = true;
        break;
    }
    instances.value = instancesData.value[type] || [];
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 修改 loadAllData 函数为只刷新当前显示的类型
const loadAllData = async () => {
  if (nodeType.value && nodeType.value !== 'embedded') {
    await loadTypeData(nodeType.value);
  }
};

// 获取节点顺序权重
const getNodeSortWeight = (node, nodeOrder) => {
  if (!nodeOrder || !Array.isArray(nodeOrder) || nodeOrder.length === 0) {
    return -1; // 如果没有定义顺序，返回默认值
  }
  
  // 查找节点类型在自定义顺序中的位置
  const index = nodeOrder.findIndex(item => 
    (item.type === node.type) || 
    (item.name && node.name && item.name.toLowerCase() === node.name.toLowerCase())
  );
  
  return index === -1 ? 999 : index; // 如果不在自定义顺序中，放到最后
};

// 修改初始化方法
const initAIModels = async () => {

  const freeNodeName = siteStore.freeNodeName;
  const normalNodeName = siteStore.normalNodeName;
  const plusNodeName = siteStore.plusNodeName;
  const claudeNodeName = siteStore.claudeNodeName;
  const embeddedNodeName = siteStore.backupNode;
  const grokNodeName = siteStore.grokNodeName;
  const sassName = siteStore.soruxGptSideBarName;
  const drawNodeName = siteStore.drawNodeName || t('common.draw'); // 使用自定义绘图节点名称，默认为多语言"绘图"

  // 解析 backupSites 字符串
  let backupSites = [];
  if (siteStore.backupSites) {
    try {
      const sitesStr = siteStore.backupSites.replace(/^\"|\"$/g, '').slice(1, -1);
      if (sitesStr) {
        const siteMatches = sitesStr.match(/\{[^}]+\}/g) || [];
        backupSites = siteMatches.map(site => {
          const nameMatch = site.match(/name=([^,}]+)/);
          const urlMatch = site.match(/url=([^,}]+)/);
          const openTypeMatch = site.match(/openType=([^,}]+)/);
          const iconUrlMatch = site.match(/iconUrl=([^,}]+)/);
          return {
            name: nameMatch ? nameMatch[1] : '',
            url: urlMatch ? urlMatch[1] : '',
            openType: openTypeMatch ? openTypeMatch[1] : '',
            iconUrl: iconUrlMatch ? iconUrlMatch[1] : ''
          };
        });
      }
    } catch (error) {
      console.error('解析备用站点失败:', error);
      backupSites = [];
    }
  }

  // 创建所有有效节点
  const validNodes = [
    { name: freeNodeName, type: 'free' },
    { name: normalNodeName, type: '4o' },
    { name: plusNodeName, type: 'plus' },
    { name: sassName, type: 'sass' },
    { name: claudeNodeName, type: 'claude' },
    { name: grokNodeName, type: 'grok' },
    ...backupSites.map(site => ({
      name: site.name,
      type: 'embedded',
      openType: site.openType,
      url: site.url,
      iconUrl: site.iconUrl
    }))
  ].filter((node) => node.name && node.name !== '');

  // 如果启用绘图功能，添加绘图节点
  if (siteStore.enableDraw === 'true' && drawNodeName) {
    validNodes.push({ name: drawNodeName, type: 'draw' });
  }

  if (validNodes.length === 0) {
    
    ElNotification.error({
      message: t('common.noValidNodes')
    });
    return false;
  }

  // 获取节点自定义排序
  let nodeOrder = [];
  try {
    if (siteStore.nodeOrder) {
      // 如果是字符串，尝试解析
      if (typeof siteStore.nodeOrder === 'string') {
        nodeOrder = JSON.parse(siteStore.nodeOrder);
      } else if (Array.isArray(siteStore.nodeOrder)) {
        // 如果已经是数组，直接使用
        nodeOrder = siteStore.nodeOrder;
      }
    }
  } catch (error) {
    console.error('解析节点排序失败:', error);
    nodeOrder = [];
  }

  // 根据自定义顺序排序节点
  if (nodeOrder && nodeOrder.length > 0) {
    validNodes.sort((a, b) => {
      const weightA = getNodeSortWeight(a, nodeOrder);
      const weightB = getNodeSortWeight(b, nodeOrder);
      return weightA - weightB;
    });
  }

  AIModels.value = validNodes;

  // 安全地应用用户偏好
  const defaultNode = applyUserPreference(validNodes);
  
  if (defaultNode) {
    activeModel.value = defaultNode.name;
    nodeType.value = defaultNode.type;
  } else {
    console.error('No valid nodes found!');
  }

  // 异步设置状态监控，不阻塞页面加载
  setupStatusMonitoring(defaultNode.name);

  // 只加载第一个节点的数据
  if (defaultNode.type !== 'embedded' && defaultNode.type !== 'grok') {
    await loadTypeData(defaultNode.type);
  }

  return true;
};

const loginCheck = async (item) => {
  selectLoading.value = true;
  try {
    if (nodeType.value !== 'claude') {
      const userData = {
        usertoken: userName.value,
        nodeType: nodeType.value,
        carid: item.carID,
        planType: item.isPlus
      };

      // 后台先校验，校验后返回真实的carid。再发送xy的oauth接口
      const realCardID = await api.authUser(userData);
      const loginData = {
        usertoken: userName.value,
        nodeType: nodeType.value,
        carid: realCardID,
        planType: item.isPlus
      };
      const response = await fetch(`/auth/login?carid=${realCardID}`, {
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
      });
      if (response.redirected) {
        Cookies.set('visitor', false, { expires: 30 });
        Cookies.set('carid', realCardID, { expires: 30 });
        window.location.href = '/';
      } else {
        ElNotification.error(t('errors.selectCarFailed'));
      }
    } else if (nodeType.value == 'claude') {
      const param = {
        usertoken: userName.value,
        carid: item.carID,
        isPlus: item.isPlus
      };
      const claudePath = await authCar(param);
      if (claudePath) {
        let claudeUrl;
        // 判断是不是启用了第三方claude
        if (siteStore.enableSxClaude == 'true' && siteStore.sxClaudeUrl) {
          claudeUrl = siteStore.sxClaudeUrl + claudePath
        } else { 
          // fuclaude 的地址
          claudeUrl = siteStore.claudeUrl + claudePath
        }
        window.location.href = claudeUrl;
      } else {
        ElNotification.error(t('errors.selectCarFailed'));
      }
    }

  } finally {
    selectLoading.value = false;
  }
};
const loginConfirm = () => {
  ElMessageBox.confirm(t('auth.loginPrompt'), t('auth.notLoggedIn'), {
    confirmButtonText: t('common.login'),
    cancelButtonText: t('common.register'),
    type: 'warning',
  })
    .then(() => {
      router.push('/login');
    })
    .catch(() => {
      router.push('/register');
    });
};
const handleClick = async (item) => {
  if (isLogin.value && userName.value) {
    if (nodeType.value === 'grok') {
      selectLoading.value = true;
      try {
        const grokPath = await api.fetchGrokLoginUrl(item.isPlus)
        if (grokPath) {
          const openType = siteStore.grokOpenType
          const url = siteStore.grokUrl + grokPath
          if (openType && openType === '_self') {
            window.location.href = url
          } else { 
            window.open(url, '_blank')
          }
        }
      } finally {
        selectLoading.value = false;
      }
    } else if (nodeType.value === 'sass') {
      selectLoading.value = true;
      try {
        const saasPath = await api.fetchSassLoginUrl()
        if (saasPath) {
          const openType = siteStore.soruxGptSideBarOpenType
          const sassUrl = siteStore.soruxGptSideBarUrl + saasPath
          if (openType && openType === '_self') {
            window.location.href = sassUrl
          } else { 
            window.open(sassUrl, '_blank')
          }
        }
      } finally {
        selectLoading.value = false;
      }
    } else if (nodeType.value === 'draw') {
      // 在当前页面显示绘图组件，而不是跳转
      showDrawComponent();
    } else {
      loginCheck(item);
    }
  } else {
    loginConfirm();
  }
};


onMounted(async () => {
  try {
    const initSuccess = await initAIModels();
    if (initSuccess) {
      loadAllData();
      // 检查滑动指示器
      checkScrollIndicators();
    }

  } catch (error) {
    console.error('初始化失败:', error);
  }
});

// 监听AIModels变化，更新滑动指示器
watch(() => AIModels.value, () => {
  checkScrollIndicators();
}, { deep: true });

// 监听activeModel变化，自动保存用户偏好
watch(() => activeModel.value, (newModel) => {
  if (newModel && nodeType.value) {
    // 延迟保存，避免频繁写入
    setTimeout(() => {
      saveUserPreference(newModel, nodeType.value);
    }, 1000);
  }
});

// 监听窗口大小变化，更新滑动指示器
onMounted(() => {
  const handleResize = () => {
    checkScrollIndicators();
  };

  window.addEventListener('resize', handleResize);

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });
});

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
  // 清理状态监控定时器
  if (statusTimer.value) {
    clearInterval(statusTimer.value);
    statusTimer.value = null;
  }

});

const STATUS = {
  IDLE: 'idle',
  BUSY: 'busy',
  OFFLINE: 'offline'
}
const statusMap = {
  [t('status.idle')]: 'idle',
  [t('status.busy')]: 'busy',
  [t('status.offline')]: 'offline',
  // 保留原始中文键以兼容现有数据
  "空闲": 'idle',
  "繁忙": 'busy',
  "停运": 'offline',
}
// 状态值映射
const getStatusValue = (status) => {
  return statusMap[status] || 'unknown'
}

// 获取状态对应的显示文本
const getInstanceStatusText = (status, detail) => {
  const statusValue = getStatusValue(status);
  if (statusValue === STATUS.IDLE) {
    return t('recommendations.recommended'); // Use 't' instead of '$t'
  } else if (statusValue === STATUS.BUSY) {
    return t('recommendations.available');
  } else if (statusValue === STATUS.OFFLINE) {
    return detail;
  }
  return t('status.unknown');
};

// 显示绘图组件的函数
const showDrawComponent = () => {
  showDrawPanel.value = true;
  showIframe.value = false;
};

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.src = chatgptLogo;
};


// 获取模型描述
// 判断是否为OpenAI模型
const isOpenAIModel = (nodeType) => {
  const openaiModels = ['free', '4o', 'plus', 'draw', 'sass'];
  return openaiModels.includes(nodeType);
}

// 判断是否为Claude模型
const isClaudeModel = (nodeType) => {
  const claudeModels = ['claude'];
  return claudeModels.includes(nodeType);
}

// 获取OpenAI状态（异步，不阻塞UI）
const fetchOpenAIStatus = async () => {
  // 避免重复请求，但不阻塞其他操作
  if (statusLoading.value) return Promise.resolve();

  try {
    statusLoading.value = true;

    // 使用Promise.race设置超时，避免长时间等待
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('请求超时')), 10000)
    );

    const fetchPromise = fetch('https://status.openai.com/api/v2/summary.json', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    const response = await Promise.race([fetchPromise, timeoutPromise]);

    if (response.ok) {
      const data = await response.json();
      // 查找Chat组件的状态
      const chatComponent = data.components?.find(component =>
        component.name.toLowerCase() === 'chat'
      );

      if (chatComponent) {
        openaiStatus.value = chatComponent.status;
      } else {
        // 如果没有找到Chat组件，使用整体状态
        openaiStatus.value = data.status?.indicator === 'none' ? 'operational' : 'degraded_performance';
      }
    } else {
      console.warn('无法获取OpenAI状态，HTTP状态:', response.status);
      openaiStatus.value = null;
    }
  } catch (error) {
    console.warn('获取OpenAI状态失败:', error.message);
    openaiStatus.value = null;
  } finally {
    statusLoading.value = false;
  }
}

// 获取Claude状态（异步，不阻塞UI）
const fetchClaudeStatus = async () => {
  // 避免重复请求，但不阻塞其他操作
  if (statusLoading.value) return Promise.resolve();

  try {
    statusLoading.value = true;

    // 使用Promise.race设置超时，避免长时间等待
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('请求超时')), 10000)
    );

    const fetchPromise = fetch('https://status.anthropic.com/api/v2/components.json', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    const response = await Promise.race([fetchPromise, timeoutPromise]);

    if (response.ok) {
      const data = await response.json();
      // 查找claude.ai组件的状态
      const claudeComponent = data.components?.find(component =>
        component.name.toLowerCase() === 'claude.ai'
      );

      if (claudeComponent) {
        claudeStatus.value = claudeComponent.status;
      } else {
        console.warn('未找到claude.ai组件');
        claudeStatus.value = null;
      }
    } else {
      console.warn('无法获取Claude状态，HTTP状态:', response.status);
      claudeStatus.value = null;
    }
  } catch (error) {
    console.warn('获取Claude状态失败:', error.message);
    claudeStatus.value = null;
  } finally {
    statusLoading.value = false;
  }
}

// 检查是否是使用OpenAI状态监控的模型
const isOpenAIStatusModel = (modelType) => {
  return isOpenAIModel(modelType) || modelType === 'sass' || modelType === 'draw';
}

// 获取状态指示器样式
const getStatusIndicatorClass = () => {
  if (statusLoading.value) {
    return 'bg-gray-400 animate-spin';
  }

  let currentStatus = null;

  if (isOpenAIStatusModel(nodeType.value)) {
    currentStatus = openaiStatus.value;
  } else if (isClaudeModel(nodeType.value)) {
    currentStatus = claudeStatus.value;
  } else {
    return 'bg-green-500 animate-pulse';
  }

  switch (currentStatus) {
    case 'operational':
      return 'bg-green-500 animate-pulse';
    case 'degraded_performance':
      return 'bg-yellow-500 animate-pulse';
    case 'partial_outage':
      return 'bg-orange-500 animate-pulse';
    case 'major_outage':
      return 'bg-red-500 animate-pulse';
    case 'maintenance':
      return 'bg-blue-500 animate-pulse';
    default:
      return 'bg-gray-400';
  }
}

// 获取状态文本
const getStatusText = (status) => {
  return t(`status.${status}`) || t('status.unknown');
}

// 异步设置状态监控
const setupStatusMonitoring = async () => {
  // 清除现有定时器
  if (statusTimer.value) {
    clearInterval(statusTimer.value);
    statusTimer.value = null;
  }

  // 清除所有状态
  openaiStatus.value = null;
  claudeStatus.value = null;

  // 根据模型类型异步获取状态
  if (isOpenAIStatusModel(nodeType.value)) {
    // 异步获取OpenAI状态，不阻塞页面加载
    fetchOpenAIStatus().catch(error => {
      console.warn('异步获取OpenAI状态失败:', error);
    });

    // 设置定时器定期更新OpenAI状态（每30秒）
    statusTimer.value = setInterval(() => {
      if (isOpenAIStatusModel(nodeType.value)) {
        fetchOpenAIStatus().catch(error => {
          console.warn('定时获取OpenAI状态失败:', error);
        });
      }
    }, 30000);

  } else if (isClaudeModel(nodeType.value)) {
    // 异步获取Claude状态，不阻塞页面加载
    fetchClaudeStatus().catch(error => {
      console.warn('异步获取Claude状态失败:', error);
    });

    // 设置定时器定期更新Claude状态（每30秒）
    statusTimer.value = setInterval(() => {
      if (isClaudeModel(nodeType.value)) {
        fetchClaudeStatus().catch(error => {
          console.warn('定时获取Claude状态失败:', error);
        });
      }
    }, 30000);
  }
}

</script>

<template>
  <div class="w-full">
    <!-- 主要内容区域 -->
    <div class="w-full max-w-10xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="w-full relative">
        <!-- 点击节点后的加载遮罩 -->
        <div v-if="selectLoading" class="fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-black/80 backdrop-blur-sm">
          <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-8 max-w-sm mx-4">
            <!-- 加载动画 -->
            <div class="flex justify-center mb-6">
              <div class="relative">
                <!-- 主要旋转环 -->
                <div class="w-20 h-20 border-4 border-gray-200 dark:border-gray-700 rounded-full animate-spin border-t-black dark:border-t-white"></div>
                <!-- 内部图标 -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <svg class="w-8 h-8 text-black dark:text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- 加载文字 -->
            <div class="text-center space-y-3">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ t('loading.enteringChat') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ t('loading.connectingToService', { model: activeModel }) }}
              </p>

              <!-- 进度指示器 -->
              <div class="flex justify-center space-x-1 mt-4">
                <div class="w-2 h-2 bg-black dark:bg-white rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-300 dark:bg-gray-700 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                <div class="w-2 h-2 bg-gray-200 dark:bg-gray-800 rounded-full animate-bounce" style="animation-delay: 0.3s"></div>
              </div>
            </div>
          </div>
        </div>



        <!-- 极简模型选择器 -->
        <div class="mb-8">
          <!-- 标题区域 -->
          <div class="text-center mb-6">
            <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {{ t('modelSelector.title') }}
            </h1>
          </div>

          <!-- 模型选择卡片 -->
          <div class="mb-6">
            <!-- 加载状态 -->
            <div v-if="!AIModels || AIModels.length === 0" class="flex items-center justify-center py-8">
              <div class="text-center">
                <svg class="animate-spin w-8 h-8 mx-auto mb-3 text-gray-400" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-gray-500 dark:text-gray-400">{{ t('modelSelector.loading') }}</span>
              </div>
            </div>

            <!-- 模型选择卡片容器 - 响应式布局 -->
            <div v-else class="relative">
              <!-- 手机端左右滑动指示器 -->
              <div class="md:hidden relative">
                <!-- 左侧滑动指示器 -->
                <button
                  v-if="showLeftArrow"
                  @click="scrollLeft"
                  class="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-all duration-200 hover:scale-110 scroll-indicator"
                  :style="{ transform: 'translateY(-50%) translateX(-50%)' }"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>

                <!-- 右侧滑动指示器 -->
                <button
                  v-if="showRightArrow"
                  @click="scrollRight"
                  class="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-all duration-200 hover:scale-110 scroll-indicator"
                  :style="{ transform: 'translateY(-50%) translateX(50%)' }"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <!-- 模型选择卡片滚动容器 -->
                <div
                  ref="modelScrollContainer"
                  @scroll="handleScroll"
                  :class="[
                    'flex gap-1',
                    // 手机端：水平滚动
                    'overflow-x-auto scrollbar-hide',
                    // 手机端：左右内边距
                    'px-6'
                  ]"
                >
                  <!-- 模型选择卡片 -->
                  <template v-for="model in AIModels" :key="model?.name || 'unknown'">
                    <button
                      v-if="model && model.name"
                      @click="handleModelClick(model)"
                      :class="[
                        'group relative flex flex-col items-center p-1 md:p-2 lg:p-3 rounded-2xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-xl model-card',
                        // 手机端：固定宽度，防止换行
                        'flex-shrink-0 w-20',
                        activeModel === model.name
                          ? 'border-blue-500 dark:border-blue-400 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 text-blue-700 dark:text-blue-300 shadow-lg shadow-blue-500/25 selected'
                          : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                      ]"
                    >
                      <!-- 选中状态指示器 -->
                      <div v-if="activeModel === model.name" 
                        class="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center shadow-lg selected-indicator
                          bg-black text-white dark:bg-white dark:text-black ring-2 ring-white dark:ring-black transition-transform duration-200 scale-100 group-hover:scale-110">
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 20 20">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" />
                        </svg>
                      </div>

                      <!-- 模型图标容器 -->
                      <div class="relative w-12 h-12 mb-2 rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center p-2 shadow-inner icon-container">
                        <img
                          v-if="model.type === 'embedded' && model.iconUrl"
                          :src="model.iconUrl"
                          :alt="(model.name || 'unknown') + ' logo'"
                          :class="getModelLogoClass(model.type || 'chatgpt', activeModel === model.name)"
                          @error="handleImageError"
                          class="w-full h-full object-contain transition-transform duration-300 group-hover:scale-110 icon-hover"
                        />
                        <img
                          v-else
                          :src="getModelLogo(model.type || 'chatgpt')"
                          :alt="(model.name || 'unknown') + ' logo'"
                          :class="getModelLogoClass(model.type || 'chatgpt', activeModel === model.name)"
                          @error="handleImageError"
                          class="w-full h-full object-contain transition-transform duration-300 group-hover:scale-110 icon-hover"
                        />
                      </div>

                      <!-- 模型名称 -->
                      <span class="text-xs font-semibold text-center leading-tight line-clamp-2">{{ model.name || '未知模型' }}</span>

                      <!-- 悬停时的光晕效果 -->
                      <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </button>
                  </template>
                </div>
              </div>

              <!-- 桌面端布局 -->
              <div class="hidden md:block relative">
                <!-- 桌面端左右滑动指示器 -->
                <button
                  v-if="showLeftArrow"
                  @click="scrollLeft"
                  class="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-all duration-200 hover:scale-110 scroll-indicator"
                  :style="{ transform: 'translateY(-50%) translateX(-50%)' }"
                >
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>

                <!-- 右侧滑动指示器 -->
                <button
                  v-if="showRightArrow"
                  @click="scrollRight"
                  class="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-all duration-200 hover:scale-110 scroll-indicator"
                  :style="{ transform: 'translateY(-50%) translateX(50%)' }"
                >
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <!-- 桌面端模型选择卡片滚动容器 -->
                <div
                  ref="desktopModelScrollContainer"
                  @scroll="handleScroll"
                  :class="[
                    'flex gap-1',
                    // 桌面端：水平滚动
                    'overflow-x-auto scrollbar-hide',
                    // 桌面端：左右内边距
                    'px-16'
                  ]"
                >
                  <!-- 模型选择卡片 -->
                  <template v-for="model in AIModels" :key="model?.name || 'unknown'">
                    <button
                      v-if="model && model.name"
                      @click="handleModelClick(model)"
                      :class="[
                        'group relative flex flex-col items-center p-1 md:p-2 lg:p-3 rounded-md border-2 transition-all duration-100 hover:scale-105 hover:shadow-xl min-w-[90px] flex-shrink-0 model-card',
                        activeModel === model.name
                          ? 'border-blue-500 dark:border-blue-400 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 text-blue-700 dark:text-blue-300 shadow-lg shadow-blue-500/25 selected'
                          : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                      ]"
                    >
                      <!-- 选中状态指示器 -->
                      <div v-if="activeModel === model.name" 
                        class="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 dark:bg-blue-400 rounded-full flex items-center justify-center shadow-lg selected-indicator">
                        <svg class="w-3.5 h-3.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </div>

                      <!-- 模型图标容器 -->
                      <div class="relative w-16 h-16 mb-2 rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center p-3 shadow-inner icon-container">
                        <img
                          v-if="model.type === 'embedded' && model.iconUrl"
                          :src="model.iconUrl"
                          :alt="(model.name || 'unknown') + ' logo'"
                          :class="getModelLogoClass(model.type || 'chatgpt', activeModel === model.name)"
                          @error="handleImageError"
                          class="w-full h-full object-contain transition-transform duration-300 group-hover:scale-110 icon-hover"
                        />
                        <img
                          v-else
                          :src="getModelLogo(model.type || 'chatgpt')"
                          :alt="(model.name || 'unknown') + ' logo'"
                          :class="getModelLogoClass(model.type || 'chatgpt', activeModel === model.name)"
                          @error="handleImageError"
                          class="w-full h-full object-contain transition-transform duration-300 group-hover:scale-110 icon-hover"
                        />
                      </div>

                      <!-- 模型名称 -->
                      <span class="text-sm font-semibold text-center leading-tight line-clamp-2">{{ model.name || '未知模型' }}</span>

                      <!-- 悬停时的光晕效果 -->
                      <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </button>
                  </template>
                </div>
              </div>
            </div>
          </div>

          <!-- 状态和公告区域 -->
          <div class="flex flex-row items-center justify-center gap-2 sm:gap-4">
            <!-- 官网状态 -->
            <div class="inline-flex items-center px-2 sm:px-4 py-1.5 sm:py-2 bg-gray-50 dark:bg-gray-800 rounded-full border border-gray-200 dark:border-gray-700">
              <!-- 状态指示器 -->
              <div
                :class="[
                  'w-1.5 h-1.5 sm:w-2 sm:h-2 mr-1.5 sm:mr-2 rounded-full transition-all duration-300',
                  getStatusIndicatorClass()
                ]"
              ></div>
              <span class="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mr-1 sm:mr-2">
                {{ t('common.officialStatus') }}:
              </span>
              <!-- AI服务状态显示 -->
              <span
                v-if="(isOpenAIStatusModel(nodeType) && openaiStatus) || (isClaudeModel(nodeType) && claudeStatus)"
                class="text-xs sm:text-sm text-gray-500 dark:text-gray-400"
              >
                {{ getStatusText(isOpenAIStatusModel(nodeType) ? openaiStatus : claudeStatus) }}
              </span>
              <span v-else class="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                {{ t('status.operational') }}
              </span>
            </div>

            <!-- 公告折叠按钮 -->
            <button
              v-if="getCurrentAnnouncement"
              @click="toggleAnnouncement"
              class="inline-flex items-center px-2 sm:px-4 py-1.5 sm:py-2 bg-gray-50 dark:bg-gray-800 rounded-full border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200"
            >
              <!-- 公告图标 -->
              <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 mr-1.5 sm:mr-2 rounded-full bg-blue-500 animate-pulse"></div>
              <span class="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mr-1 sm:mr-2">
                {{ t('common.announcement') }}
              </span>
              <!-- 展开/折叠图标 -->
              <svg
                :class="[
                  'w-3 h-3 sm:w-4 sm:h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200',
                  isAnnouncementExpanded ? 'rotate-180' : ''
                ]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>

          <!-- 展开的公告内容区域 - 性能优化版本 -->
          <div
            v-if="getCurrentAnnouncement && isAnnouncementExpanded"
            class="mt-4 mx-auto"
          >
            <div
              class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden transform transition-all duration-200 ease-out"
              style="animation: slideDown 0.2s ease-out;"
            >
              <!-- Loading状态 -->
              <div v-if="isAnnouncementLoading" class="p-4 flex items-center justify-center">
                <div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                  <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-sm">加载中...</span>
                </div>
              </div>

              <!-- 公告内容 - 优化的iframe -->
              <div v-else class="p-4">
                <iframe
                  ref="announcementIframe"
                  :srcdoc="announcementContentCache || getOptimizedAnnouncementContent()"
                  class="w-full border-0 rounded-md bg-transparent transition-all duration-200"
                  style="min-height: 120px; height: 200px;"
                  @load="handleAnnouncementIframeLoad"
                  sandbox="allow-scripts"
                  title="公告内容"
                  scrolling="auto"
                  loading="eager"
                ></iframe>
              </div>
            </div>
          </div>

        </div>



        <!-- 内容区域 - 自动撑开 -->
        <div class="w-full">
          <div v-if="nodeType === 'embedded'" class="w-full">
            <div v-if="showIframe"
              class="border dark:border-gray-700 border-gray-300 rounded shadow-lg iframe-wrapper">
              <iframe :src="iframeUrl" frameborder="0" class="w-full h-[calc(100vh-200px)] min-h-[600px]" :title="activeModel"></iframe>
            </div>
          </div>
          <!-- 绘图组件部分 -->
          <div v-else-if="showDrawPanel">
            <!-- 使用Image组件 -->
            <Image />
          </div>
          <div v-else class="pb-6">
            <!-- 数据加载状态 -->
            <div v-if="loading" class="flex flex-col items-center justify-center py-16">
              <div class="relative mb-6">
                <!-- 外层旋转圆环 -->
                <div class="w-16 h-16 border-4 border-gray-200 dark:border-gray-700 rounded-full animate-spin border-t-black dark:border-t-white"></div>
                <!-- 内层脉冲圆点 -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-black dark:bg-white rounded-full animate-pulse"></div>
                <!-- 中心小圆点 -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full"></div>
              </div>

              <!-- 加载文字 -->
              <div class="text-center space-y-2">
                <p class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ t('loading.loadingData') }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ t('loading.loadingModelInstances', { model: activeModel }) }}
                </p>
              </div>

              <!-- 加载进度指示器 -->
              <div class="mt-6 flex space-x-1">
                <div class="w-2 h-2 bg-black dark:bg-white rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-300 dark:bg-gray-700 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>

            <!-- 实例网格 -->
            <div v-else class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 2xl:grid-cols-6 gap-2 sm:gap-3 md:gap-4">
              <!-- 极简空状态 -->
              <div v-if="!loading && instances.length === 0"
                class="col-span-full flex flex-col items-center justify-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                  <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-1">{{ t('common.noValidNodes') }}</h3>
              </div>

              <!-- 极简实例卡片 -->
              <div v-else @click="handleClick(instance)" v-for="(instance, index) in instances" :key="instance.id"
                class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-600"
                :style="{ animationDelay: `${index * 100}ms` }"
              >
                <div class="sm:p-1 md:p-4">
                  <!-- 卡片头部 - logo、carID、账号类型 -->
                  <div class="flex items-center mb-2 sm:mb-3 gap-1 sm:gap-2">
                    <!-- 左侧：AI模型Logo -->
                    <div class="flex items-center flex-shrink-0">
                      <div class="w-6 h-6 sm:w-8 sm:h-8 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center p-1">
                        <img
                          :src="getModelLogo(nodeType)"
                          :alt="nodeType + ' logo'"
                          :class="getModelLogoClass(nodeType, false, true)"
                          @error="handleImageError"
                        />
                      </div>
                    </div>

                    <!-- 中间：实例名称 - 允许收缩但保持最小宽度 -->
                    <div class="flex-1 min-w-0">
                      <h3 class="text-xs sm:text-base font-semibold text-gray-900 dark:text-white truncate">
                        {{ instance.carID }}
                      </h3>
                    </div>

                    <!-- 右侧：账号类型标签 - 固定宽度，不被挤压 -->
                    <div class="flex items-center flex-shrink-0">
                      <span class="text-xs px-1.5 sm:px-2 py-0.5 sm:py-1 rounded font-medium bg-black text-white dark:bg-white dark:text-black whitespace-nowrap">
                        {{ getAccountType(nodeType, instance.isPlus) }}
                      </span>
                    </div>
                  </div>
                  <!-- 状态信息 -->
                  <div class="flex items-center justify-between mb-2 sm:mb-3">
                    <span class="text-xs sm:text-sm text-gray-600 dark:text-gray-400 truncate">
                      {{ t(`status.${getStatusValue(instance.status)}`) }}
                    </span>
                    <span class="text-xs text-gray-500 dark:text-gray-400 truncate ml-1">
                      {{ getInstanceStatusText(instance.status, instance.detail) }}
                    </span>
                  </div>

                  <!-- 极简进度条 -->
                  <div class="mb-1 sm:mb-2">
                    <div class="h-1.5 sm:h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                      <div class="h-full transition-all duration-500 ease-out rounded-full" :class="{
                        'bg-green-500': getStatusValue(instance.status) === 'idle',
                        'bg-yellow-500': getStatusValue(instance.status) === 'busy',
                        'bg-red-500': getStatusValue(instance.status) === 'offline'
                      }" :style="{
                        width: getStatusValue(instance.status) === 'offline' ? '100%' : ((1 - instance.count / (instance.count > 200 ? (instance.count * 2) : 200)) * 100 + '%')
                      }"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-backtop />
          </div>
        </div>
      </div>
    </div>

    <!-- 可选：移动端菜单弹出层 -->
    <div v-if="showMobileMenu" class="md:hidden fixed inset-0 z-50 bg-black/50" @click="showMobileMenu = false">
      <!-- 菜单内容 -->
    </div>

    <!-- 可选：用户菜单弹出层 -->
    <div v-if="showUserMenu" class="md:hidden fixed inset-0 z-50 bg-black/50" @click="showUserMenu = false">
      <!-- 用户菜单内容 -->
    </div>
  </div>
</template>
<style scoped>
/* 极简淡入动画 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 简约进度条动画 */
.progress-bar {
  animation: progressLoad 1s ease-out;
  transform-origin: left;
}

@keyframes progressLoad {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* iframe 样式 */
.iframe-wrapper {
  flex: 1;
  min-height: 0;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

iframe {
  border-radius: 0.5rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
  /* 手机端保持2列布局，优化卡片内容 */
  .grid {
    gap: 0.5rem; /* 8px */
  }

  /* 手机端卡片内边距优化 */
  .grid > div {
    padding: 0.75rem; /* 12px */
  }
}

/* 加载状态 */
.el-loading-mask {
  border-radius: 0.5rem;
  background: rgba(0, 0, 0, 0.05) !important;
}

/* 卡片悬停效果 */
.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 状态指示器 */
.status-indicator {
  transition: all 0.2s ease;
}

/* 标签样式 */
.tag {
  transition: all 0.2s ease;
}

/* 进度条容器 */
.progress-container {
  background: #f3f4f6;
}

.dark .progress-container {
  background: #374151;
}

/* 文字选择 */
::selection {
  background-color: #000;
  color: #fff;
}

.dark ::selection {
  background-color: #fff;
  color: #000;
}

/* 焦点状态 */
.card:focus {
  outline: 2px solid #000;
  outline-offset: 2px;
}

.dark .card:focus {
  outline-color: #fff;
}

/* 禁用状态 */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 简约边框 */
.border-simple {
  border: 1px solid #e5e7eb;
}

.dark .border-simple {
  border-color: #374151;
}

/* 简约阴影 */
.shadow-simple {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.dark .shadow-simple {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

/* 悬停提升效果 */
.hover-lift:hover {
  transform: translateY(-1px);
}

/* 过渡效果 */
.transition-simple {
  transition: all 0.2s ease;
}

/* 居中的模型选择器样式 */
.model-selector-center {
  display: flex;
  justify-content: center;
  width: 100%;
}

.model-selector-tabs {
  display: inline-flex;
  background: #f3f4f6;
  border-radius: 0.5rem;
  padding: 0.25rem;
  gap: 0.125rem;
  max-width: 100%;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.model-selector-tabs::-webkit-scrollbar {
  display: none;
}

.dark .model-selector-tabs {
  background: #374151;
}

/* 模型选择按钮样式 */
.model-tab-button {
  position: relative;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  white-space: nowrap;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.model-tab-button.active {
  background: #000000;
  color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .model-tab-button.active {
  background: #ffffff;
  color: #000000;
}

.model-tab-button:not(.active) {
  color: #6b7280;
  background: transparent;
}

.dark .model-tab-button:not(.active) {
  color: #9ca3af;
}

.model-tab-button:not(.active):hover {
  background: #ffffff;
  color: #000000;
}

.dark .model-tab-button:not(.active):hover {
  background: #4b5563;
  color: #ffffff;
}

/* 隐藏滚动条 */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 下拉菜单动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 右侧滑入动画 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}



.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 手机端和桌面端模型选择器滑动优化 */
.model-selector-horizontal {
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch; /* iOS 滑动优化 */
}

.model-selector-horizontal > button {
  scroll-snap-align: center;
}

/* 居中显示优化 */
.flex.justify-center {
  justify-content: center;
}

/* 当内容不需要滚动时的居中效果 */
.flex.overflow-x-auto.justify-center {
  justify-content: center;
}

/* 确保卡片在居中时有合适的间距 */
.flex.overflow-x-auto.justify-center > button {
  margin: 4px;
}

/* 优化滚动容器的居中行为 */
.flex.overflow-x-auto.justify-center {
  justify-content: center;
}

.flex.overflow-x-auto.justify-start {
  justify-content: flex-start;
}

/* 桌面端滑动优化 */
@media (min-width: 768px) {
  .overflow-x-auto {
    scroll-behavior: smooth;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    /* 为卡片边框留出空间 */
    padding: 4px 0;
  }

  .overflow-x-auto > button {
    scroll-snap-align: start;
    /* 确保卡片有足够空间显示边框和阴影 */
    margin: 4px;
  }
}

/* 手机端模型选择器样式优化 */
@media (max-width: 768px) {
  /* 模型选择器容器 */
  .flex.overflow-x-auto {
    scroll-behavior: smooth;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    /* 添加滚动指示器 */
    scrollbar-width: none;
    -ms-overflow-style: none;
    /* 为卡片边框留出空间 */
    padding: 4px 0;
  }

  .flex.overflow-x-auto::-webkit-scrollbar {
    display: none;
  }

  /* 模型卡片滑动对齐 */
  .flex.overflow-x-auto > button {
    scroll-snap-align: start;
    /* 确保卡片有足够空间显示边框和阴影 */
    margin: 4px;
  }

  /* 第一个和最后一个卡片的边距调整 */
  .flex.overflow-x-auto > button:first-child {
    margin-left: 0;
  }

  .flex.overflow-x-auto > button:last-child {
    margin-right: 1rem; /* 右侧留出空间 */
  }
}

/* 公告iframe样式 */
.announcement-iframe {
  border: none;
  border-radius: 0.375rem;
  background: transparent;
}

/* 移除了复杂的CSS沙盒样式，现在使用iframe完全隔离 */

/* 公告展开动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 公告折叠按钮样式 */
.announcement-toggle {
  transition: all 0.2s ease;
}

.announcement-toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dark .announcement-toggle:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .model-selector-tabs {
    padding: 0.125rem;
    gap: 0.0625rem;
  }

  .announcement-content :deep(h1) { font-size: 1.125rem; }
  .announcement-content :deep(h2) { font-size: 1rem; }
  .announcement-content :deep(h3) { font-size: 0.875rem; }

  .model-tab-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }

  /* 移动端模型选择器优化 */
  .model-selector-horizontal {
    padding: 0.5rem;
  }

  .model-selector-horizontal > button {
    min-width: 4rem;
    padding: 0.75rem 0.5rem;
  }
}

/* 平板端优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .model-selector-horizontal > button {
    min-width: 5rem;
    padding: 1rem 0.75rem;
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .model-selector-horizontal > button {
    min-width: 6rem;
    padding: 1.25rem 1rem;
  }
}

/* 新增：模型选择卡片样式 */
.model-card {
  position: relative;
  overflow: hidden;
  /* 确保边框和阴影不被遮挡 */
  z-index: 1;
  /* 添加外边距来为边框和阴影留出空间 */
  margin: 4px;
  /* 确保边框完全可见 */
  border-radius: 1rem;
  /* 防止边框被裁剪 */
  box-sizing: border-box;
}
@media (max-width: 640px) {
  .model-card {
    padding: 0.5rem !important;
  }
}

.model-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
  z-index: 1;
}

.model-card:hover::before {
  left: 100%;
}

/* 选中状态的光晕效果 - 优化边框显示 */
.model-card.selected {
  background: #e8f1ff; /* 浅蓝，亮色主题 */
  border-color: #000 !important; /* 黑色 */
  color: #1e293b;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.18);
  /* 保证边框宽度和样式 */
  border-width: 2px !important;
  border-style: solid !important;
}

.dark .model-card.selected {
  background: #1e293b; /* 深蓝灰，暗色主题 */
  border-color: #fff !important; /* 白色 */
  color: #e0e7ef;
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.22);
  border-width: 2px !important;
  border-style: solid !important;
}

/* 悬停状态优化 */
.model-card:hover {
  /* 确保悬停时边框不被遮挡 */
  border-width: 2px !important;
  border-style: solid !important;
  /* 提升z-index确保边框可见 */
  z-index: 2;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dark .model-card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* 图标容器的内阴影效果 */
.icon-container {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .icon-container {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 文字截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 滑动指示器的毛玻璃效果 */
.scroll-indicator {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  /* 确保箭头按钮可以正常点击 */
  pointer-events: auto;
  z-index: 20;
}

/* 手机端箭头按钮优化 */
@media (max-width: 768px) {
  .scroll-indicator {
    /* 增加点击区域 */
    min-width: 44px;
    min-height: 44px;
    /* 确保在手机端有足够的点击区域 */
    touch-action: manipulation;
  }
}



/* 选中状态指示器的动画 */
.selected-indicator {
  animation: pulse 2s infinite;
  z-index: 10;
  top: -3px !important;
  right: -3px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.18);
  transition: transform 0.2s cubic-bezier(0.4,0,0.2,1);
}
.selected-indicator:active {
  transform: scale(0.95);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 图标悬停时的缩放效果 */
.icon-hover {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-hover:hover {
  transform: scale(1.1) rotate(5deg);
}

/* 渐变背景的微妙动画 */
.gradient-bg {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 响应式卡片大小调整 */
@media (max-width: 640px) {
  .model-card {
    min-width: 6rem;
    max-width: 6rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .model-card {
    min-width: 8rem;
    max-width: 8rem;
  }
}

@media (min-width: 1025px) {
  .model-card {
    min-width: 10rem;
    max-width: 10rem;
  }
}

/* 无障碍访问优化 */
.model-card:focus {
  outline: 2px solid #1e293b;
  outline-offset: 2px;
}

.dark .model-card:focus {
  outline-color: #1e293b;
}

/* 加载状态的骨架屏效果 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
