package org.seven.share.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.util.CarIdUtils;
import org.seven.share.mapper.ClaudeSessionMapper;
import org.seven.share.common.pojo.dto.CarStatus;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.entity.ClaudeSessionEntity;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.ClaudeSessionService;
import org.seven.share.service.LicenseValidator;
import org.seven.share.service.xyhelper.ApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.seven.share.common.util.CarStatusUtils.convertToCarInfoVo;
import static org.seven.share.common.util.ConstantUtil.*;

/**
 * @ClassName: ClaudeSessionServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/12
 */
@Service
@Slf4j
public class ClaudeSessionServiceImpl extends ServiceImpl<ClaudeSessionMapper, ClaudeSessionEntity> implements ClaudeSessionService {

    @Resource
    private ClaudeSessionMapper claudeSessionMapper;

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${api-auth}")
    private String apiAuth;

    @Autowired
    @Qualifier("commonAsyncExecutor")
    private Executor commonAsyncExecutor;

    @Resource
    ApiService apiService;

    @Resource
    private LicenseValidator licenseValidator;

    private final List<CarInfoVo> cachedProCarList = new ArrayList<>(); // 用于缓存生成的车号
    private final List<CarInfoVo> cachedMaxCarList = new ArrayList<>(); // 用于缓存生成的车号

    // 缓存实例，设置1分钟过期时间
    private final LoadingCache<String, List<CarInfoVo>> carListCache = Caffeine.newBuilder()
            .expireAfterWrite(2, TimeUnit.MINUTES) // 5分钟后过期
            .maximumSize(10) // 最大缓存100个分页结果
            .build(key -> fetchCarListFromApi(1, 1000));

    @Scheduled(cron = "0 */1 * * * ?") // 每分钟执行一次
     public void refreshCache() {
        String sxClaudeUrl = chatGptConfigService.getValueByKey("sxClaudeUrl");
        if (StrUtil.isNotEmpty(sxClaudeUrl)) {
            log.info("定时刷新缓存，key: {}", sxClaudeUrl);
            carListCache.refresh(sxClaudeUrl); // 异步刷新
        } else {
            log.warn("url地址为空，刷新claude车队缓存失败");
        }
    }

    @PostConstruct
    public void init() {
        String sxClaudeUrl = chatGptConfigService.getValueByKey("sxClaudeUrl");
        if (StrUtil.isNotEmpty(sxClaudeUrl)) {
            carListCache.refresh(sxClaudeUrl);
        }
    }

    @Override
    public String authUserInfoAndGetLoginUrl(String userToken, String carId, Integer isPlus) {
        // 判断用户是否存在
        ChatGptUserEntity user = chatGptUserService.getUserInfoByUsername(userToken);
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户信息不存在");
        }
        // 用户对应车号权限校验
        checkClaudeAccessByUsername(user, isPlus);
        String loginToken = user.getLoginToken();
        // 获取使用sx-claude权限信息
        Map<String, String> map = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("sxClaudeUrl", "enableSxClaude", "thirdClaudeType"));
        boolean claudeAccess = Optional.ofNullable(map.get("enableSxClaude")).map(Boolean::parseBoolean).orElse(false)
                && licenseValidator.haveAccess("claude");
        String sxClaudeUrl = map.get("sxClaudeUrl");
        String thirdClaudeType = map.get("thirdClaudeType");
        if (claudeAccess && StrUtil.isNotEmpty(sxClaudeUrl)) {
            String carIdByPlanType = findCarIdByPlanType(sxClaudeUrl, carId, isPlus);
            String loginUrl;
            if (StrUtil.isEmpty(thirdClaudeType) || "sx".equals(thirdClaudeType)) {
                loginUrl= "/auth/logintoken?usertoken=" + loginToken + "&carid=" + carIdByPlanType + "&isPlus=" + isPlus;
            } else {
                loginUrl= "/logintoken?usertoken=" + loginToken + "&carid=" + carIdByPlanType + "&isPlus=" + isPlus;
            }
            log.info("claude login url：{}", loginUrl);
            return loginUrl;
        }
        // 查询数据库查找claude信息
        ClaudeSessionEntity claude = getRealClaudeSession(carId);

        // 用于fuclaude生成登录地址返回前端
        return generateFuClaudeLoginUrl(loginToken, claude);
    }
    /**
     * 获取claude信息，如果是虚拟车就根据用户的权益获取对应的节点
     * @param carId
     * @return
     */
    private ClaudeSessionEntity getRealClaudeSession(String carId) {
        ClaudeSessionEntity claude = this.lambdaQuery()
                .eq(ClaudeSessionEntity::getCarID, carId)
                .eq(ClaudeSessionEntity::getStatus, CLAUDE_ENABLE_STATUS)
                .one();
        if (ObjectUtil.isEmpty(claude)) {
            log.info("用户使用了虚拟claude pro");
            // 用户选择了虚拟车，则随机选择一个空闲的pro账号
            claude = getClaudeInfoByPlanType(PLAN_TYPE_CLAUDE_PRO);
        }
        return claude;
    }

    /**
     * 检查用户的claude权限
     */
    private void checkClaudeAccessByUsername(ChatGptUserEntity user, Integer isPro) {
        LocalDateTime now = LocalDateTime.now();
        // 如果是pro账号，要校验pro的过期时间
        if (PLAN_TYPE_CLAUDE_PRO.equals(isPro) || PLAN_TYPE_CLAUDE_MAX.equals(isPro)) {
            if (ObjectUtil.isEmpty(user.getClaudeProExpireTime()) || user.getClaudeProExpireTime().isBefore(now)) {
                log.warn("{}的Claude Pro权益已到期，请重新购买高级权益", user.getUserToken());
                throw new CustomException("您还没有claude pro的使用权限，请使用普通节点或者升级套餐权益");
            }
        } else {
            // claude普号校验plus是否过期，如果pro权限过期则校验claude普通权益是否过期
            if (ObjectUtil.isEmpty(user.getClaudeProExpireTime()) || user.getClaudeProExpireTime().isBefore(now)) {
                if (ObjectUtil.isEmpty(user.getClaudeExpireTime()) || user.getClaudeExpireTime().isBefore(now)) {
                    log.warn("{}的Claude权益已到期，请重新购买高级权益", user.getUserToken());
                    throw new CustomException("您的Claude基础权益已到期，请重新购买高级权益");
                }
            }
        }
    }

    /**
     * 生成claude登录地址
     * @param loginToken
     * @param claudeSession
     * @return
     */
    private String generateFuClaudeLoginUrl(String loginToken, ClaudeSessionEntity claudeSession) {
        String claudeUrl = chatGptConfigService.getValueByKey("claudeUrl");
        if (StrUtil.isEmpty(claudeUrl)) {
            throw new CustomException("管理员还未配置claude地址");
        }

        log.info("获取claude的登录地址...");
        String session = claudeSession.getOfficialSession();
        Map<String, Object> map = new HashMap<>();
        map.put("session_key", session);
        // 通过不同用户名来实现对话隔离
        map.put("unique_name", loginToken);
        map.put("expires_in", 86400);
        log.info("请求claude登录信息的参数为：{}", map);
        // 发送获取登录地址的请求
        try {
            String url = claudeUrl + CLAUDE_LOGIN_SUFFIX;
            log.info("claude url:{}", url);
            ResponseEntity<String> response = restTemplate.postForEntity(url, map, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                String body = response.getBody();
                // 将string解析成json
                JsonNode jsonNode = objectMapper.readTree(body);
                log.info("请求claude登录信息的响应体解析结果：{}", jsonNode);
                if (jsonNode.has("detail") && "invalid sessionKey".equals(jsonNode.get("detail").asText())) {
                    throw new CustomException("sessionKey失效");
                } else if (jsonNode.has("login_url")) {
                    String loginUrl = jsonNode.get("login_url").asText();
                    String encodedUserToken = URLEncoder.encode(loginToken, StandardCharsets.UTF_8);
                    String fullLoginUrl = loginUrl + "&username=" + encodedUserToken;
                    log.info("生成的Claude登录地址为:{}", fullLoginUrl);
                    return fullLoginUrl;
                }
            } else {
                log.error("请求claude登录地址失败，响应为:{}", response.getStatusCode());
                claudeSession.setStatus(1); // 失效
                claudeSession.setRemark(response.getBody());
                claudeSessionMapper.updateById(claudeSession);
                throw new CustomException("获取sess异常，请更换其他节点");
            }
        }catch (Exception e) {
            log.error("请求claude登录地址失败:", e);
            claudeSession.setStatus(1); // 失效
            claudeSession.setRemark(e.getMessage());
            claudeSessionMapper.updateById(claudeSession);
            throw new CustomException("获取sess异常，请更换其他节点");
        }
        return null;
    }

    @Override
    public String generatorCarID() {
        return CarIdUtils.generatorCarID();
    }


    @Override
    public String getClaudeLoginUrl(String username) {
        ChatGptUserEntity user = chatGptUserService.getUserByLoginToken(username);
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("选车失败：用户信息不存在");
        }
        // 判断是否有使用claude的权限
        Integer claudeType = getClaudeByUserAccess(user);

        Map<String, String> map = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("sxClaudeUrl", "enableSxClaude", "thirdClaudeType"));
        String sxClaudeUrl = map.get("sxClaudeUrl");
        String enableSxClaude = map.get("enableSxClaude");
        String thirdClaudeType = map.get("thirdClaudeType");
        String loginToken = user.getLoginToken();
        if (Objects.equals(enableSxClaude, "true") && StrUtil.isNotEmpty(sxClaudeUrl)) {
            String carIdByPlanType = findCarIdByPlanType(sxClaudeUrl, null, claudeType);
            String loginUrl;
            log.info("thirdClaudeType:{}", thirdClaudeType);
            if (StrUtil.isEmpty(thirdClaudeType) || "sx".equals(thirdClaudeType)) {
                log.info("开始获取sx-claude的登录地址");
                loginUrl = "/auth/logintoken?usertoken=" + loginToken + "&carid=" + carIdByPlanType + "&isPlus=" + claudeType;
            } else {
                log.info("开始获取lyy-claude的登录地址");
                loginUrl = "/logintoken?usertoken=" + loginToken + "&carid=" + carIdByPlanType + "&isPlus=" + claudeType;
            }
            log.info("claude login url :{}", loginUrl);
            return loginUrl;
        }
        ClaudeSessionEntity claudeSession = getClaudeInfoByPlanType(claudeType);
        return generateFuClaudeLoginUrl(loginToken, claudeSession);
    }

    /**
     * 根据用户权限，查到对应的claude账号。
     * @param user
     * @return
     */
    private Integer getClaudeByUserAccess(ChatGptUserEntity user) {
        LocalDateTime claudeExpireTime = user.getClaudeExpireTime();
        LocalDateTime proClaudeExpireTime = user.getClaudeProExpireTime();
        LocalDateTime now = LocalDateTime.now();
        Integer claudeType;
        // 如果用户的claude pro有过期时间并且未过期，则给用户选择pro
        if(ObjectUtil.isNotEmpty(proClaudeExpireTime) && proClaudeExpireTime.isAfter(now)){
            claudeType = PLAN_TYPE_CLAUDE_PRO;
        } else if (ObjectUtil.isNotEmpty(claudeExpireTime) && claudeExpireTime.isAfter(now)) {
            claudeType = PLAN_TYPE_CLAUDE_FREE;
        } else {
            throw new CustomException("您无权限使用claude，请续费充值后重试。");
        }

        return claudeType;

    }

    private ClaudeSessionEntity getClaudeInfoByPlanType(Integer claudeType) {
        ClaudeSessionEntity claudeSession = getClaudeByType(claudeType);
        if (ObjectUtil.isEmpty(claudeSession) && PLAN_TYPE_CLAUDE_PRO.equals(claudeType)) {
            log.warn("当前没有可用的pro账号，服务降级，切换普通claude账号");
            claudeSession = getClaudeByType(PLAN_TYPE_CLAUDE_FREE);
            if (ObjectUtil.isEmpty(claudeSession)) {
                throw new CustomException("选车失败:当前暂无空闲节点可切换，请联系管理员新增节点。");
            }
        }
        return claudeSession;
    }

    private ClaudeSessionEntity getClaudeByType(Integer claudeType) {
        QueryWrapper<ClaudeSessionEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("status", CLAUDE_ENABLE_STATUS)
        // resetsAt 为空或者小于等于当前时间
                .and(w -> w.isNull("resetsAt")
                        .or()
                        .le("resetsAt", String.valueOf(System.currentTimeMillis() / 1000)))
                .last("ORDER BY RAND() LIMIT 1");
        wrapper.eq("isPro", claudeType);
        return claudeSessionMapper.selectOne(wrapper);
    }

    @Override
    @CacheEvict(cacheNames = "claude_list", allEntries = true)
    public void updateRemaining(Map<String, String> paramMap, HttpServletRequest request) {
        String header = request.getHeader(API_AUTH_HEADER);
        log.info("请求头：{}", header);
        // 判断是否本程序的请求
        if (!Objects.equals(header, apiAuth)) {
            throw new ServiceException(403, "您无权限调用此接口");
        }
        String email = paramMap.getOrDefault("email", "");
        String remaining = paramMap.getOrDefault("remaining", "");
        String resetsAt = paramMap.getOrDefault("resetsAt", "");
        log.info("请求中的邮箱：{}，剩余次数：{}，重置时间：{}", email, remaining, resetsAt);
        if (StrUtil.isEmpty(email)) {
            throw new CustomException("claude次数更新失败：邮箱为空");
        }
        if (StrUtil.isEmpty(remaining)) {
            remaining = "0";
        }
        UpdateWrapper<ClaudeSessionEntity> wrapper = new UpdateWrapper<>();
        wrapper.set("remaining", remaining);
        wrapper.set("resetsAt", resetsAt);
        wrapper.eq("email", email);
        update(wrapper);
        log.info("claude次数更新完成...");
    }

    @Override
    public List<CarInfoVo> getClaudeList(Integer page, Integer size) {
        List<String> statusOrder = Arrays.asList("空闲", "繁忙", "停运", "翻车");
        List<CarInfoVo> carInfoVoList;
        Map<String, String> map = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("enableSxClaude", "sxClaudeUrl"));
        Boolean enableSxClaude = Optional.ofNullable(map.get("enableSxClaude")).map(Boolean::parseBoolean).orElse(false);
        String sxClaudeUrl = map.get("sxClaudeUrl");
        if (enableSxClaude && StrUtil.isNotEmpty(sxClaudeUrl)) {
            List<CarInfoVo> cachedList = carListCache.get(sxClaudeUrl);
            if (cachedList == null) {
                cachedList = new ArrayList<>();
            }
            carInfoVoList = new ArrayList<>(cachedList); // 复制缓存中的列表
        } else {
            carInfoVoList = fetchLocalCarList(page, size);
        }
        Map<String, String> configMap = chatGptConfigService.getKeyValueMapByKeys(
                Arrays.asList("virtualClaudeNo", "virtualClaudeNameList", "virtualClaudeMaxNo", "virtualClaudeMaxNameList"));

        // 添加虚拟车队
        if (carInfoVoList.stream().anyMatch(e -> PLAN_TYPE_CLAUDE_PRO.equals(e.getIsPlus()))) {
            addClaudeVirtualCars(carInfoVoList, configMap.get("virtualClaudeNo"), configMap.get("virtualClaudeNameList"), cachedProCarList, PLAN_TYPE_CLAUDE_PRO);
        }
        // 添加Max虚拟车队
        if (carInfoVoList.stream().anyMatch(e -> PLAN_TYPE_CLAUDE_MAX.equals(e.getIsPlus()))) {
            addClaudeVirtualCars(carInfoVoList, configMap.get("virtualClaudeMaxNo"), configMap.get("virtualClaudeMaxNameList"), cachedMaxCarList, PLAN_TYPE_CLAUDE_MAX);
        }
        return carInfoVoList.stream()
                .sorted(Comparator.comparing(CarInfoVo::getIsPlus, Comparator.reverseOrder())
                        .thenComparing(e -> statusOrder.indexOf(e.getStatus()))
                        .thenComparing(CarInfoVo::getCount)).toList();

    }

    private List<CarInfoVo> fetchLocalCarList(Integer page, Integer size) {
        List<ClaudeSessionEntity> list = this.list(new Page<>(page, size), new LambdaQueryWrapper<ClaudeSessionEntity>()
                .eq(ClaudeSessionEntity::getStatus, CLAUDE_ENABLE_STATUS)
                .isNull(ClaudeSessionEntity::getDeletedAt));
        return list.stream().map(e -> {
            CarInfoVo carInfoVo = new CarInfoVo();
            carInfoVo.setCarID(e.getCarID());
            carInfoVo.setCount(new Random().nextLong(120));
            carInfoVo.setIsPlus(e.getIsPro());
            checkTimeStatus(e.getResetsAt(), carInfoVo);
            return carInfoVo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据账号权限查找出对于的查号
     */
    private String findCarIdByPlanType(String sxClaudeUrl, String carId, Integer planType) {
        if (StrUtil.isNotEmpty(sxClaudeUrl)) {
            List<CarInfoVo> carInfoVoList = carListCache.get(sxClaudeUrl);
            if (carInfoVoList == null) {
                throw new CustomException("没有空闲的claude账号，请联系管理员");
            }
            if (carInfoVoList.stream().anyMatch(car -> Objects.equals(car.getCarID(), carId))){
                return carId;
            }
            // gpt页面选车或者虚拟车的时候，从远程拿一个数据匹配。
            Optional<CarInfoVo> first = carInfoVoList.stream().filter(e -> Objects.equals(e.getIsPlus(), planType) && !Objects.equals(e.getStatus(), "停运")).findFirst();
            if (first.isPresent()) {
                return first.get().getCarID();
            } else {
                throw new CustomException("没有空闲的claude账号，请联系管理员");
            }
        }
        return null;
    }
    /**
     * 远程获取sx-claude的车队数据
     *
     * @return
     */
    public List<CarInfoVo> fetchCarListFromApi(int page, int size) {
        long start = System.currentTimeMillis();
        String sxClaudeUrl = chatGptConfigService.getValueByKey("sxClaudeUrl");
        // 构建请求
        Map<String, Integer> requestData = Map.of("page", page, "size", size);
        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建HttpEntity
        HttpEntity<Map<String, Integer>> requestEntity = new HttpEntity<>(requestData, headers);

        ResponseEntity<String> response = restTemplate.exchange(sxClaudeUrl + "/carpage", HttpMethod.POST, requestEntity, String.class);
        if (!response.getStatusCode().equals(HttpStatus.OK)) {
            log.error("sx-claude url获取车号请求失败，失败原因: {}", response);
            return Collections.emptyList();
        }
        String body = response.getBody();
        JsonNode jsonNode;
        try {
            jsonNode = objectMapper.readTree(body);
        } catch (JsonProcessingException e) {
            log.error("解析claude 节点数据失败：{}",e.getMessage());
            return Collections.emptyList();
        }

        // 检查状态码
        int code = jsonNode.get("code").asInt();
        if (code != 1000) {
            log.error("sx-claude url获取车号请求失败，失败原因: {}", jsonNode);
            return Collections.emptyList();
        }
        JsonNode data = jsonNode.get("data");
        if (data == null || !data.has("list")) {
            return Collections.emptyList();
        }

        // 获取车辆列表
        JsonNode listNode = data.get("list");
        List<String> carIds = new ArrayList<>();
        for (JsonNode carNode : listNode) {
            carIds.add(carNode.get("carID").asText());
        }
        log.info("claude 车队有[{}]", carIds.size());
        // 并发获取车辆状态，设置超时时间
        List<CompletableFuture<CarInfoVo>> futures = carIds.stream()
                .map(carId -> CompletableFuture.supplyAsync(() -> {
                    try {
                        CarStatus carStatus = fetchCarStatus(sxClaudeUrl, carId);
                        log.debug("claude 车号：{}, status:{}", carId, carStatus != null ? carStatus.getAccountReady() : "null");
                        if (carStatus != null && "true".equals(carStatus.getAccountReady())) {
                            int isPlus = getIsPlus(carStatus);
                            return convertToCarInfoVo(carId, isPlus, carStatus, false);
                        }
                    } catch (Exception e) {
                        log.warn("处理车号 [{}] 时发生异常: {}", carId, e.getMessage());
                    }
                    return null;
                }, commonAsyncExecutor))
                .toList();

        // 设置总体超时时间为30秒，避免客户端长时间等待
        List<CarInfoVo> carInfoVos = new ArrayList<>();
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.get(30, java.util.concurrent.TimeUnit.SECONDS); // 30秒超时

            carInfoVos = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(1, java.util.concurrent.TimeUnit.SECONDS); // 每个任务最多等1秒
                        } catch (Exception e) {
                            log.debug("获取单个车辆状态超时或失败: {}", e.getMessage());
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(CarInfoVo::getIsPlus).reversed().thenComparing(CarInfoVo::getCount))
                    .collect(Collectors.toList());
        } catch (java.util.concurrent.TimeoutException e) {
            log.warn("获取Claude车队状态总体超时，返回已获取的部分结果");
            // 获取已完成的结果
            carInfoVos = futures.stream()
                    .filter(CompletableFuture::isDone)
                    .map(future -> {
                        try {
                            return future.get();
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(CarInfoVo::getIsPlus).reversed().thenComparing(CarInfoVo::getCount))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取Claude车队状态时发生异常", e);
        }

        log.info("claude状态正常的车队有[{}]", carInfoVos.size());
        long end = System.currentTimeMillis();
        log.info("获取claude 车队状态耗时[{}ms]", end - start);
    return carInfoVos;
    }

    private int getIsPlus(CarStatus carStatus) {
        int isPlus;
        if (Objects.equals(carStatus.getPlanType(), "pro")) {
            isPlus = 1;
        }else if (Objects.equals(carStatus.getPlanType(), "max")) {
            isPlus = 2;
        } else if (Objects.equals(carStatus.getPlanType(), "free")){
            isPlus = 0;
        } else {
            isPlus = "true".equals(carStatus.getIsPlus()) ? 1 : 0;
        }
        return isPlus;
    }


    private CarStatus fetchCarStatus(String sxClaudeUrl, String carId) {
        try {
            ResponseEntity<CarStatus> responseEntity = restTemplate.getForEntity(String.format(sxClaudeUrl + "/status?carid=%s", carId), CarStatus.class);
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                return responseEntity.getBody();
            }
        } catch (Exception e) {
            log.error("获取车号 [{}] 状态失败", carId, e);
        }
        return null;
    }

    /**
     *
     * @param loginToken
     * @param carId
     * @param isPlus
     */
    @Override
    public Map<String, Object> oauthUserInfo(String loginToken, String carId, Integer isPlus) {
        ChatGptUserEntity user = chatGptUserService.getUserByLoginToken(loginToken);
        if (ObjectUtil.isEmpty(user)) {
            log.error("根据loginToken：{}查询用户信息失败：", loginToken);
            throw new CustomException("用户信息查询失败");
        }
        checkClaudeAccessByUsername(user, isPlus);
        LocalDateTime claudeExpireTime = user.getClaudeExpireTime();
        LocalDateTime claudeProExpireTime = user.getClaudeProExpireTime();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime resultTime = claudeExpireTime != null && claudeExpireTime.isAfter(now) ?
                claudeExpireTime :
                claudeProExpireTime != null && claudeProExpireTime.isAfter(now) ?
                        claudeProExpireTime : now;
        return Map.of("code", 1, "expireTime", resultTime);
    }

    @Override
    @CacheEvict(cacheNames = "claude_list", allEntries = true)
    public void updateClaudeSession(ClaudeSessionEntity claudeSession) {
        boolean flag = "true".equals(chatGptConfigService.getValueByKey("enableSxClaude"));
        if (flag) {
            apiService.sendSaveOrUpdateRequest(claudeSession, "update", "claude");
        } else {
            updateById(claudeSession);
        }
    }

    @Override
    @CacheEvict(cacheNames = "claude_list", allEntries = true)
    public void saveClaudeSession(ClaudeSessionEntity claudeSession) {
        boolean flag = "true".equals(chatGptConfigService.getValueByKey("enableSxClaude"));
        if (flag) {
            apiService.sendSaveOrUpdateRequest(claudeSession, "add", "claude");
        } else {
            save(claudeSession);
        }
    }

    @Override
    @CacheEvict(cacheNames = "claude_list", allEntries = true)
    public void removeCarInfoBatch(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        boolean flag = "true".equals(chatGptConfigService.getValueByKey("enableSxClaude"));
        if (flag) {
            apiService.sendDeleteRequest(ids, "claude");
        } else {
            this.removeBatchByIds(ids);
        }
    }

    private void checkTimeStatus(String resetsAt, CarInfoVo carInfoVo) {
        try {
            // 将字符串转换为 long 类型时间戳
            long timestamp = Long.parseLong(resetsAt);

            // 获取当前时间的时间戳（秒级）
            long currentTime = Instant.now().getEpochSecond();
            // 比较时间戳
            if (timestamp < currentTime) {
                setCountAndStatus(carInfoVo);
            } else {
                // 计算秒值差
                long secondsUntilRecovery = timestamp - currentTime;
                carInfoVo.setStatus("停运");
                carInfoVo.setCount(200L);
                carInfoVo.setDetail("将于" + secondsUntilRecovery + "秒后恢复");
            }
        } catch (Exception e) {
            setCountAndStatus(carInfoVo);
        }
    }

    private static void setCountAndStatus(CarInfoVo carInfoVo) {
        if (carInfoVo.getCount() > 60) {
            carInfoVo.setStatus("繁忙");
            carInfoVo.setDetail("可用");
        } else {
            carInfoVo.setStatus("空闲");
            carInfoVo.setDetail("推荐");
        }
    }

    private void addClaudeVirtualCars(List<CarInfoVo> carList, String virtualClaudeNo, String virtualClaudeNameList, List<CarInfoVo> cachedCarList, Integer isPlus) {
        if (CollectionUtil.isEmpty(carList)) return ;
        List<String> status = Arrays.asList("空闲", "繁忙");
        log.info("新增[{}]个claude pro虚拟车...", virtualClaudeNo);
        if (StrUtil.isNotEmpty(virtualClaudeNo) && Long.parseLong(virtualClaudeNo) > 0) {
            long num = Long.parseLong(virtualClaudeNo);
            // 如果缓存中已经有车号列表并且与设置的虚拟数量一致，直接返回
            if (cachedCarList.size() == num) {
                carList.addAll(cachedCarList);
                return;
            }
            cachedCarList.clear();  // 如果虚拟数量不同，清空缓存列表
            Random random = new Random();
            for (int i = 0; i < num; i++) {
                CarInfoVo carInfoVo = new CarInfoVo();
                carInfoVo.setCarID(getVirtualCarName(i, virtualClaudeNameList));
                carInfoVo.setIsPlus(isPlus);

                String randomStatus = isFreeTime() ? status.get(0) : status.get(random.nextInt(status.size()));
                int count;
                if ("空闲".equals(randomStatus)) {
                    count = random.nextInt(60); // 空闲状态下 count 范围是 0 到 19
                } else {
                    count = 60 + random.nextInt(60); // 繁忙状态下 count 范围是 20 到 39
                }
                // 凌晨清空虚拟车的次数
                if (isFreeTime()) {
                    count = 0;
                }
                CarStatus carStatus = new CarStatus();
                carStatus.setIsPlus(String.valueOf(isPlus));
                carStatus.setAccountReady("true");
                carStatus.setClears_in(0);
                carStatus.setCount(count);

                carInfoVo.setCount((long) count);
                carInfoVo.setStatus(randomStatus);
                carInfoVo.setDetail("空闲".equals(randomStatus)? "推荐": "可用");
                carInfoVo.setCarStatus(carStatus);
                carList.add(carInfoVo);
                cachedCarList.add(carInfoVo);
            }
        }
    }
    private String getVirtualCarName(int num, String virtualClaudeNameList) {
        if (StrUtil.isEmpty(virtualClaudeNameList)) {
            return CarIdUtils.generatorCarID();
        }

        String[] carNames = virtualClaudeNameList.split(",");
        return (num >= 0 && num < carNames.length) ? carNames[num] : CarIdUtils.generatorCarID();
    }

    private boolean isFreeTime() {
        LocalDateTime now = LocalDateTime.now();
        // 将时间范围定义为 LocalTime 对象
        LocalTime start = LocalTime.of(0, 0); // 凌晨 12 点
        LocalTime end = LocalTime.of(8, 0);   // 早上 8 点
        // 提取当前时间的 LocalTime 部分
        LocalTime currentTime = now.toLocalTime();
        return currentTime.isAfter(start) && currentTime.isBefore(end);
    }

}
