package org.seven.share.controller.client;

import lombok.RequiredArgsConstructor;
import org.seven.share.common.annotation.RateLimit;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.service.ChatGptSessionService;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.ClaudeSessionService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/client-api")
@RequiredArgsConstructor
public class CarController {
    private final ChatGptUserService chatGptUserService;

    private final ClaudeSessionService claudeSessionService;

    private final ChatGptSessionService chatGptSessionService;

    /**
     * 获取gpt页面的车队列表
     * @param type
     * @param page
     * @param size
     * @return
     */
    @GetMapping("/openai/carpage")
    @RateLimit(20)
    public R getCarList(@RequestParam String type,
                        @RequestParam(value = "page", defaultValue = "1") Integer page,
                        @RequestParam(value = "size", defaultValue = "1000") Integer size) {
        Map<String, Object> map = chatGptUserService.getCarList(type, page, size);
        return R.ok(map);
    }

    /**
     * 游客模式，获取登录信息
     * @return
     */
    @GetMapping("/getAccessToken")
    public R getAccessToken() {
        Map<String, String> map = chatGptUserService.getAccessTokenAndCarId();
        return R.ok(map);
    }

    /**
     * 选择相同账号类型的空闲车
     * @param username
     * @return
     */

    @GetMapping("/getIdleCar")
    public Map<String, String> getIdleCar(String username) {
        return chatGptSessionService.getIdleCar(username);
    }

    /**
     * 获取用户当前权益最高的车和用户和节点类型
     * @param userId
     * @return
     */
    @GetMapping("/session/getIdleByUser")
    public R getIdleCarByUserExpire(String userId){
        Map<String, String> map = chatGptSessionService.getIdleCarByUserExpire(userId);
        return R.ok(map);
    }

    @GetMapping("/claude/carpage")
    @RateLimit(20)
    public R list(@RequestParam(value = "page", defaultValue = "1") Integer page,
                  @RequestParam(value = "size", defaultValue = "1000") Integer size) {
        List<CarInfoVo> cluadeList= claudeSessionService.getClaudeList(page, size);
        return R.ok(cluadeList);
    }

    @GetMapping("/grok/carpage")
    @RateLimit(20)
    public R pageGrok() {
        List<CarInfoVo> grokCarPage = chatGptUserService.getGrokCarPage();
        List<CarInfoVo> list = grokCarPage.stream()
                .sorted(Comparator.comparing(CarInfoVo::getIsPlus)
                        .reversed()
                .thenComparing(CarInfoVo::getCount)).toList();
        return R.ok(list);
    }

    @GetMapping("/sass/carpage")
    @RateLimit(20)
    public R pageSass() {
        List<CarInfoVo> grokCarPage = chatGptUserService.getSassCarPage();
        List<CarInfoVo> list = grokCarPage.stream().sorted(Comparator.comparing(CarInfoVo::getCount)).toList();
        return R.ok(list);
    }


    @PostMapping("/updateClaudeRemaining")
    public R updateRemaining(@RequestBody Map<String, String> paramMap, HttpServletRequest request) {
        claudeSessionService.updateRemaining(paramMap, request);
        return R.ok();
    }

}
