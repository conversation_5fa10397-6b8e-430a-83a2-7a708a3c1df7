<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { CheckboxValueType } from 'element-plus';
import { addSubType, fetchGetTenantList, updateSubType } from '@/service/api';

export type OperateType = 'create' | 'edit' | 'copy';

// 定义类型接口
interface ModelLimit {
  limit: number;
  per: string;
}

interface ModelLimits {
  [key: string]: ModelLimit;
}

interface FormDataType {
  id: string | number;
  name: string;
  money: string | number;
  validDays: number;
  isPlus: number;
  sort: number;
  updateTime: string;
  createTime: string;
  features: string[];
  modelLimits: ModelLimits;
  subType: string;
  exclusive: number;
  exclusiveType: number;
  isHotSale: number;
  isPro: number;
  isSuper: number;
  isNotValued: number;
  [key: string]: any;
}

interface Props {
  visible: boolean;
  operateType: OperateType;
  data?: any;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'submitted'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const tenantList = ref<Array<{ tenantId: string; tenantName: string }>>([]);

const formRef = ref();
const activeNames = ref('1');
const isMobile = computed(() => window.innerWidth < 768);
const dialogWidth = computed(() => (isMobile.value ? '95%' : '50%'));

// 模型类型定义
const modelTypes = [
  { id: 'gpt-4o', name: 'gpt-4o', subType: ['1', '3', '5', '7'] },
  { id: 'o3', name: 'o3', subType: ['1', '3', '5', '7'] },
  { id: 'o1-pro', name: 'o1-pro', subType: ['1', '3', '5', '7'] },
  { id: 'o4-mini', name: 'o4-mini', subType: ['1', '3', '5', '7'] },
  { id: 'o4-mini-high', name: 'o4-mini-high', subType: ['1', '3', '5', '7'] },
  { id: 'gpt-4-5', name: 'gpt-4-5', subType: ['1', '3', '5', '7'] },
  { id: 'claude-3.5', name: 'claude-3.5', subType: ['2', '3', '6', '7'] },
  { id: 'grok-3', name: 'grok-3', subType: ['4', '5', '6', '7'] },
  { id: 'research', name: 'research', subType: ['1', '3', '5', '7'] }
];

// 速率限制选项
const limits = [
  { value: '1s', label: '每秒' },
  { value: '1m', label: '每分' },
  { value: '1h', label: '每小时' },
  { value: '3h', label: '每3小时' },
  { value: '1d', label: '每天' },
  { value: '1w', label: '每周' },
  { value: '1M', label: '每月' },
  { value: '1y', label: '每年' }
];

// 订阅类型选项
const subTypes = [
  { value: '1', label: 'ChatGPT套餐' },
  { value: '2', label: 'Claude套餐' },
  { value: '3', label: 'ChatGPT&Claude套餐' },
  { value: '4', label: 'Grok套餐' },
  { value: '5', label: 'ChatGPT&Grok套餐' },
  { value: '6', label: 'Claude&Grok套餐' },
  { value: '7', label: 'Chatgpt&Claude&Grok套餐' },
  { value: '8', label: '绘画套餐' }
];

// 功能标签选项
const options = [
  { value: '官方同款功能和UI，一比一还原体验。', label: '官方同款功能和UI，一比一还原体验。' },
  { value: '降低使用门槛：支持免梯直登，无需任何魔法', label: '降低使用门槛：支持免梯直登，无需任何魔法' },
  { value: '支持对话隔离，保护用户隐私。', label: '支持对话隔离，保护用户隐私。' },
  {
    value: '支持插件功能：语音、联网、绘画、识图、文档解析、深度思考。',
    label: '支持插件功能：语音、联网、绘画、识图、文档解析、深度思考。'
  },
  { value: '支持GPT全模型：官网有的模型，我们都有。', label: '支持GPT全模型：官网有的模型，我们都有。' },
  {
    value: '内置本地Deepseek R1满血版，免受官网【服务器繁忙】的困扰。',
    label: '内置本地Deepseek R1满血版，免受官网【服务器繁忙】的困扰。'
  },
  { value: '支持Grok全模型，响应最快，主打丝滑。', label: '支持Grok全模型，响应最快，主打丝滑。' },
  { value: '支持Claude3.7，超长上下文，理工科必备生产力。', label: '支持Claude3.7，超长上下文，理工科必备生产力。' },
  { value: '专属客服解答：提供售前咨询，售后保障。', label: '专属客服解答：提供售前咨询，售后保障。' },
  {
    value: '智能AI绘图：文生图功能，将文字描述转化为精美图像。',
    label: '智能AI绘图：文生图功能，将文字描述转化为精美图像。'
  },
  {
    value: '智能图像编辑：上传图片进行风格化改造和智能编辑。',
    label: '智能图像编辑：上传图片进行风格化改造和智能编辑。'
  },
  {
    value: '多种绘图风格选择：写实、动漫、油画、素描等多种风格可选。',
    label: '多种绘图风格选择：写实、动漫、油画、素描等多种风格可选。'
  },
  {
    value: '高清图像生成：支持生成高分辨率精美图像。',
    label: '高清图像生成：支持生成高分辨率精美图像。'
  },
  {
    value: '图像放大与优化：将模糊或低分辨率图像转换为高清晰度版本。',
    label: '图像放大与优化：将模糊或低分辨率图像转换为高清晰度版本。'
  },
  {
    value: '专业提示词指导：内置提示词模板，让AI绘图更加精准。',
    label: '专业提示词指导：内置提示词模板，让AI绘图更加精准。'
  }
];

const formData = ref<FormDataType>({
  id: '',
  name: '',
  money: '',
  validDays: 0,
  isPlus: 0,
  sort: 1,
  updateTime: '',
  createTime: '',
  features: [],
  modelLimits: {},
  subType: '',
  exclusive: 0,
  exclusiveType: 0,
  isHotSale: 0,
  isPro: 0,
  isSuper: 0,
  isNotValued: 0
});

const rules = {
  name: [{ required: true, message: '请输入订阅名称', trigger: 'blur' }],
  validDays: [{ required: true, message: '请输入有效天数', trigger: 'blur' }],
  money: [{ required: true, message: '请输入订阅金额', trigger: 'blur' }],
  subType: [{ required: true, message: '请选择订阅类型', trigger: 'blur' }]
};

// 处理订阅类型变更
const handleSubTypeChange = () => {
  if (formData.value.subType) {
    // 保存现有的限制设置
    const existingLimits = formData.value.modelLimits || {};

    // 初始化新的限制设置
    const newLimits: ModelLimits = {};
    modelTypes
      .filter(m => m.subType.includes(formData.value.subType))
      .forEach(model => {
        // 如果存在现有设置则保留，否则使用默认值
        newLimits[model.id] = existingLimits[model.id] || {
          limit: 0,
          per: ''
        };
      });

    // 更新 modelLimits
    formData.value.modelLimits = newLimits;
  }
  formData.value.exclusive = 0;
};

// 处理全选功能标签
const handleCheckAll = (val: CheckboxValueType) => {
  formData.value.features = val ? options.map(item => item.value) : [];
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (props.operateType === 'create' || props.operateType === 'copy') {
          await addSubType(formData.value);
        } else {
          await updateSubType(formData.value);
        }
        ElMessage.success('保存成功');
        emit('update:visible', false);
        emit('submitted');
      } catch {
        ElMessage.error('保存失败');
      }
    }
  });
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
  formData.value = {
    id: '',
    name: '',
    money: '',
    validDays: 0,
    isPlus: 0,
    sort: 1,
    updateTime: '',
    createTime: '',
    features: [],
    modelLimits: {},
    subType: '',
    exclusive: 0,
    exclusiveType: 0,
    isHotSale: 0,
    isPro: 0,
    isSuper: 0,
    isNotValued: 0
  } as FormDataType;
};

// 监听 props 变化，更新表单数据
watch(
  () => props.data,
  newVal => {
    if (newVal && (props.operateType === 'edit' || props.operateType === 'copy')) {
      // 确保 modelLimits 对象的结构完整
      const modelLimits = newVal.modelLimits || {};
      const processedModelLimits: ModelLimits = {};

      // 根据当前订阅类型初始化所有可用模型的限制
      modelTypes
        .filter(m => m.subType.includes(String(newVal.subType)))
        .forEach(model => {
          processedModelLimits[model.id] = modelLimits[model.id] || {
            limit: 0,
            per: ''
          };
        });

      // 更新表单数据
      const baseData = {
        ...newVal,
        modelLimits: processedModelLimits,
        features: newVal.features || []
      } as FormDataType;

      // 如果是复制模式，清除ID并修改名称
      if (props.operateType === 'copy') {
        baseData.id = '';
        baseData.name = `${newVal.name} - 副本`;
        baseData.createTime = '';
        baseData.updateTime = '';
      }

      formData.value = baseData;
    }
    fetchGetTenantList().then(res => {
      tenantList.value = (res.data || []) as Array<{ tenantId: string; tenantName: string }>;
      tenantList.value.unshift({ tenantId: '000000', tenantName: '主站' });
    });
  },
  { immediate: true }
);
</script>

<template>
  <ElDialog
    :model-value="visible"
    :title="operateType === 'create' ? '新增订阅' : operateType === 'copy' ? '复制订阅' : '编辑订阅'"
    :width="dialogWidth"
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="formData" label-width="auto" :rules="rules">
      <ElTabs v-model="activeNames" type="border-card">
        <ElTabPane label="基本信息" name="1">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="订阅名称" prop="name">
                <ElInput v-model="formData.name" placeholder="请输入订阅名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="订阅类型" prop="subType">
                <ElSelect v-model="formData.subType" placeholder="请选择订阅类型" @change="handleSubTypeChange">
                  <ElOption v-for="item in subTypes" :key="item.value" :label="item.label" :value="item.value" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="所属分站" prop="tenantId">
                <ElSelect v-model="formData.tenantId" placeholder="请选择所属分站（可选）" clearable>
                  <ElOption
                    v-for="item in tenantList"
                    :key="item.tenantId"
                    :label="item.tenantName"
                    :value="item.tenantId"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="订阅金额" prop="money">
                <ElInput v-model="formData.money" placeholder="请输入订阅金额" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="有效天数" prop="validDays">
                <ElInputNumber v-model="formData.validDays" placeholder="请输入有效天数" />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="formData.subType === '8'" :span="12">
              <ElFormItem label="绘画额度" prop="drawQuota">
                <ElInputNumber v-model="formData.drawQuota" placeholder="请输入绘画额度" />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElFormItem v-if="formData.subType === '1'" label="是否独享" prop="exclusive">
            <ElSwitch
              v-model="formData.exclusive"
              inline-prompt
              active-text="独享"
              inactive-text="共享"
              :active-value="1"
              :inactive-value="0"
            />
          </ElFormItem>
          <ElFormItem v-if="formData.exclusive === 1" label="独享类型" prop="exclusiveType">
            <ElRadioGroup v-model="formData.exclusiveType">
              <ElRadio :value="0" size="large">Free</ElRadio>
              <ElRadio :value="1" size="large">Plus</ElRadio>
              <ElRadio :value="2" size="large">Team</ElRadio>
              <ElRadio :value="3" size="large">Pro</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="显示热卖标志" prop="isHotSale">
            <ElSwitch v-model="formData.isHotSale" :active-value="1" :inactive-value="0" />
          </ElFormItem>
          <ElFormItem v-if="['1', '3', '5', '7'].includes(formData.subType)" label="Plus权限" prop="isPlus">
            <ElSwitch
              v-model="formData.isPlus"
              inline-prompt
              active-text="可用ChatGPT Plus"
              inactive-text="可用ChatGPT 普号"
              :active-value="1"
              :inactive-value="0"
            />
          </ElFormItem>
          <ElFormItem v-if="['2', '3', '6', '7'].includes(formData.subType)" label="Pro权限" prop="isPro">
            <ElSwitch
              v-model="formData.isPro"
              inline-prompt
              active-text="可用Claude Pro"
              inactive-text="可用Claude 普号"
              :active-value="1"
              :inactive-value="0"
            />
          </ElFormItem>
          <ElFormItem v-if="['4', '5', '6', '7'].includes(formData.subType)" label="Super权限" prop="isSuper">
            <ElSwitch
              v-model="formData.isSuper"
              inline-prompt
              active-text="可用Grok super"
              inactive-text="可用Grok 普号"
              :active-value="1"
              :inactive-value="0"
            />
          </ElFormItem>
          <ElFormItem label="前台展示" prop="isNotValued">
            <ElSwitch v-model="formData.isNotValued" :active-value="0" :inactive-value="1" />
          </ElFormItem>
        </ElTabPane>

        <ElTabPane v-if="formData.subType !== '8'" label="模型速率限制（请先选择订阅类型）" name="2">
          <ElDivider>
            <span class="text-base text-red-500 font-bold">周期/次数有一个为空则不限制，次数为0则禁止使用</span>
          </ElDivider>
          <div
            v-for="model in modelTypes.filter(m => m.subType.includes(formData.subType))"
            :key="model.id"
            class="mb-4"
          >
            <ElRow>
              <ElCol :span="12">
                <ElFormItem :prop="`modelLimits.${model.id}.per`" :label="`${model.name}速率周期`">
                  <ElSelect
                    v-model="formData.modelLimits[model.id].per"
                    :placeholder="`请选择${model.name}速率限制周期`"
                    clearable
                  >
                    <ElOption v-for="item in limits" :key="item.value" :label="item.label" :value="item.value" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem :prop="`modelLimits.${model.id}.limit`" :label="`${model.name}使用次数`">
                  <ElInputNumber
                    v-model="formData.modelLimits[model.id].limit"
                    :placeholder="`请输入${model.name}限制次数`"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>
        </ElTabPane>

        <ElTabPane label="其他信息" name="3">
          <ElFormItem label="商品标签" prop="features">
            <ElCheckbox
              v-if="formData.features"
              :model-value="formData.features.length === options.length"
              :indeterminate="formData.features.length > 0 && formData.features.length < options.length"
              @change="handleCheckAll"
            >
              全选
            </ElCheckbox>
            <ElSelect
              v-model="formData.features"
              multiple
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              placeholder="选择或者手动输入商品标签"
              clearable
            >
              <ElOption v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="排序" prop="sort">
            <ElInputNumber v-model="formData.sort" :min="1" :max="9999" />
          </ElFormItem>
        </ElTabPane>
      </ElTabs>

      <div class="m-4 text-right">
        <ElButton @click="handleClose">取 消</ElButton>
        <ElButton type="primary" @click="handleSubmit">保 存</ElButton>
      </div>
    </ElForm>
  </ElDialog>
</template>

<style scoped></style>
