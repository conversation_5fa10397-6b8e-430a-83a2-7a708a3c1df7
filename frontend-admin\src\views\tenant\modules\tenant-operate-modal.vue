<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { useForm, useFormRules } from '@/hooks/common/form';
import { $t } from '@/locales';
import { enableStatusOptions } from '@/constants/business';
import { createTenant, updateTenant } from '@/service/api';
const importUrl = `${import.meta.env.VITE_SERVICE_BASE_URL}/common/upload-logo`;
defineOptions({ name: 'TenantOperateModal' });

export type OperateType = UI.TableOperateType;

interface Props {
  operateType: OperateType;
  rowData?: Api.TenantManage.Tenant | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useForm();
const { defaultRequiredRule } = useFormRules();

type RuleKey = Exclude<keyof Model, 'contactUserName' | 'expireTime' | 'remark' | 'contactPhone' | 'siteLogo'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  tenantId: defaultRequiredRule,
  tenantName: defaultRequiredRule,
  domain: defaultRequiredRule,
  status: defaultRequiredRule,
  commissionRatio: defaultRequiredRule,
  siteName: defaultRequiredRule,
  commissionBalance: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: $t('page.tenant.addTenant'),
    edit: $t('page.tenant.editTenant')
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.TenantManage.Tenant,
  | 'tenantId'
  | 'tenantName'
  | 'contactUserName'
  | 'contactPhone'
  | 'commissionRatio'
  | 'commissionBalance'
  | 'remark'
  | 'domain'
  | 'expireTime'
  | 'status'
> & {
  siteName: string;
  siteLogo: string;
};

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    tenantId: '',
    tenantName: '',
    contactUserName: '',
    contactPhone: '',
    remark: '',
    domain: '',
    expireTime: '',
    status: '1',
    commissionRatio: 0.0,
    commissionBalance: 0.0,
    siteName: '',
    siteLogo: ''
  };
}

function handleInitModel() {
  model.value = createDefaultModel();

  if (!props.rowData) return;

  if (props.operateType === 'edit') {
    Object.assign(model.value, props.rowData);
  }
}

function closeModal() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const params = { ...model.value };

  const { error } = props.operateType === 'add' ? await createTenant(params) : await updateTenant(params);
  if (!error) {
    window.$message?.success($t('common.operationSuccess'));
    closeModal();
    emit('submitted');
  } else {
    window.$message?.error($t('common.operationFailed'));
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    window.$message?.error('只允许上传图片');
    return false;
  }
  if (!isLt2M) {
    window.$message?.error('图片大小超过2MB');
    return false;
  }
  return true;
};

const handleUploadSuccess = (response: { code: string; data: string }) => {
  if (response.code === '200') {
    model.value.siteLogo = response.data;
    window.$message?.success('上传成功');
  } else {
    window.$message?.error('上传失败');
  }
};

defineExpose({});
</script>

<template>
  <ElDialog v-model="visible" :title="title" preset="card" class="xs:w-300px w-800px">
    <ElForm ref="formRef" class="p-4" :model="model" :rules="rules" label-placement="left" :label-width="125">
      <ElRow :gutter="20">
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.form.tenantId')" prop="tenantId">
            <ElInput
              v-model="model.tenantId"
              :placeholder="$t('page.tenant.form.tenantId')"
              :disabled="operateType === 'edit'"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.form.tenantName')" prop="tenantName">
            <ElInput v-model="model.tenantName" :placeholder="$t('page.tenant.form.tenantName')" />
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.form.contactUserName')" prop="contactUserName">
            <ElInput v-model="model.contactUserName" :placeholder="$t('page.tenant.form.contactUserName')" />
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.form.contactPhone')" prop="contactPhone">
            <ElInput v-model="model.contactPhone" :placeholder="$t('page.tenant.form.contactPhone')" />
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.form.domain')" prop="domain">
            <ElInput v-model="model.domain" :placeholder="$t('page.tenant.form.domain')" />
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.form.commissionRatio')" prop="commissionRatio">
            <ElInputNumber
              v-model="model.commissionRatio"
              class="w-full"
              :placeholder="$t('page.tenant.form.commissionRatio')"
              :min="0"
              :max="1"
              :precision="2"
              :step="0.1"
            />
          </ElFormItem>
        </ElCol>
        <ElCol v-if="props.operateType === 'edit'" :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.form.commissionBalance')" prop="commissionBalance">
            <ElInputNumber
              v-model="model.commissionBalance"
              class="w-full"
              :min="0"
              :precision="2"
              :placeholder="$t('page.tenant.form.commissionBalance')"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.expireTime')" prop="expireTime">
            <ElDatePicker
              v-model="model.expireTime"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              :placeholder="$t('page.tenant.form.expireTime')"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.status')" prop="status">
            <ElRadioGroup v-model="model.status">
              <ElRadio v-for="{ label, value } in enableStatusOptions" :key="value" :value="value" :label="$t(label)" />
            </ElRadioGroup>
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.siteName')" prop="siteName">
            <ElInput v-model="model.siteName" :placeholder="$t('page.tenant.form.siteName')" />
          </ElFormItem>
        </ElCol>
        <ElCol :xs="24" :sm="24" :md="12">
          <ElFormItem :label="$t('page.tenant.siteLogo')" prop="siteLogo">
            <ElUpload
              class="avatar-uploader"
              :show-file-list="false"
              :on-success="handleUploadSuccess"
              :before-upload="beforeUpload"
              :action="importUrl"
              accept="image/*"
            >
              <img v-if="model.siteLogo" :src="model.siteLogo" class="avatar" />
              <ElIcon v-else class="avatar-uploader-icon"><Plus /></ElIcon>
            </ElUpload>
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem :label="$t('page.tenant.form.remark')" prop="remark">
            <ElInput v-model="model.remark" type="textarea" :rows="4" :placeholder="$t('page.tenant.form.remark')" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElSpace :size="16" class="float-right">
        <ElButton @click="closeModal">{{ $t('common.cancel') }}</ElButton>
        <ElButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<style scoped>
.avatar-uploader {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
