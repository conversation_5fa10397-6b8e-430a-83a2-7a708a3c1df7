package org.seven.share.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.enums.QuotaChangeType;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.pojo.dto.*;
import org.seven.share.common.pojo.entity.*;
import org.seven.share.common.util.CarIdUtils;
import org.seven.share.common.util.IpUtils;
import org.seven.share.common.util.JwtUtil;
import org.seven.share.common.util.SecurityUtil;
import org.seven.share.mapper.*;
import org.seven.share.common.enums.LoginType;
import org.seven.share.common.enums.SubscriptionType;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.common.pojo.vo.StatisticVo;
import org.seven.share.common.pojo.vo.UserInfoVo;
import org.seven.share.common.pojo.vo.UserPayLogsVo;
import org.seven.share.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


import static org.seven.share.common.util.CarStatusUtils.*;
import static org.seven.share.common.util.ConstantUtil.*;
import static org.seven.share.common.util.DateTimeUtil.*;
import static org.seven.share.common.util.HttpUtils.*;
import static org.seven.share.common.util.IpUtils.getClientIp;
import static org.seven.share.common.util.ServletUtils.getHost;
import static org.seven.share.constant.CacheConstant.*;



/**
 * @ClassName: UserTokenServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/22
 */
@Service
@Slf4j
public class ChatGptUserServiceImpl extends ServiceImpl<ChatGptUserMapper, ChatGptUserEntity> implements ChatGptUserService {

    @Resource
    private ChatGptUserMapper chatGptUserMapper;

    @Resource
    private ChatGptSessionMapper chatGptSessionMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ChatGptPayLogsMapper chatGptPayLogsMapper;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private EmailService emailService;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    private final Map<String, String> contentMap = new HashMap<>();

    @Resource
    private HttpServletRequest request;

    @Resource
    private BaseSysUserMapper baseSysUserMapper;

    @Resource
    private ChatGptConversationsMapper conversationsDao;

    @Resource
    private ChatGptSubTypeService chatGptSubTypeService;

    @Resource
    private SignInRecordsMapper signInRecordsMapper;

    @Resource
    private ChatGptAffRecordMapper chatGptAffRecordMapper;

    @Autowired
    @Qualifier("commonAsyncExecutor")
    private Executor commonAsyncExecutor;

    @Resource
    private ChatGptSubTypeMapper chatGptSubTypeMapper;

    @Resource
    private LicenseValidator licenseValidator;

    private boolean needVirtual = true;

    private final List<String> status = Arrays.asList("空闲", "繁忙");

    @Resource
    private SysTenantService tenantService;

    @Resource
    private UserDrawingQuotaService drawingQuotaService;

    @Resource QuotaChangeRecordService changeRecordService;

    @Resource
    private SysTenantService sysTenantService;

    // 添加缓存
    private final Cache<String, CarStatus> carStatusCache = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES) // 缓存10分钟后失效
            .maximumSize(1000) // 缓存最大容量
            .build();

    private final List<CarInfoVo> cachedPlusCarList = new CopyOnWriteArrayList<>(); // 用于缓存生成的车号
    private final List<CarInfoVo> cachedTeamCarList = new CopyOnWriteArrayList<>(); // 用于缓存生成的车号
    private final List<CarInfoVo> cachedProCarList = new CopyOnWriteArrayList<>(); // 用于缓存生成的车号
    private final List<CarInfoVo> cachedGrokCarList = new CopyOnWriteArrayList<>(); // 用于缓存生成的车号

    private final List<CarInfoVo> cachedSassCarList = new CopyOnWriteArrayList<>(); // 用于缓存生成的车号

    @Override
    public ChatGptUserEntity createUser(UserDto userDto) {
        Long l = chatGptUserMapper.selectCount(new LambdaQueryWrapper<ChatGptUserEntity>()
                .eq(ChatGptUserEntity::getUserToken, userDto.getUserToken()).isNull(ChatGptUserEntity::getDeletedAt));
        if (l > 0) {
            throw new CustomException("用户名已存在");
        }
        List<String> ids = userDto.getIds();
        LocalDateTime exclusiveExpireTime = userDto.getExclusiveExpireTime();
        boolean bind = !CollectionUtils.isEmpty(ids) && ObjectUtil.isNotEmpty(exclusiveExpireTime);
        if (bind) {
            String collect = String.join(",", ids);
            userDto.setCarids(collect);
        }
        userDto.setPassword(DigestUtils.md5DigestAsHex(userDto.getPassword().getBytes()));
        userDto.setAffCode(RandomUtil.randomStringUpper(6)); //新增邀请码
        userDto.setLoginToken(IdUtil.simpleUUID()); // 生成认证token
        String drawRegisterCount = chatGptConfigService.getValueByKey("drawRegisterCount");
        chatGptUserMapper.insert(userDto);
        // 绑定独享车
        if (bind) {
            chatGptSessionMapper.bindUserGptSession(userDto.getId(), ids, exclusiveExpireTime);
        }
        // 新增绘画额度
        long count = Optional.ofNullable(drawRegisterCount).map(Long::parseLong).orElse(0L);
        drawingQuotaService.insertDrawQuota(Collections.singletonList(userDto.getId()), count);
        return userDto;
    }

    @Override
    public String authUserInfo(String userToken, String carId, String type, Integer planType) {
        log.info("userToken:{}, carId:{}, nodeType:{}, planType:{}", userToken, carId, type, planType);
        // 免费的车不做校验，直接放行
        if ("free".equals(type)) {
            return carId;
        }
        // 根据用户token获取用户信息
        ChatGptUserEntity user = getUserInfoByUserToken(userToken, type);
        LocalDateTime plusExpireTime = user.getPlusExpireTime();
        // 如果是sass节点，则校验plus过期时间是否过期即可
        if (Objects.equals(type, "pro")) {
            if (Objects.isNull(plusExpireTime) || plusExpireTime.isBefore(LocalDateTime.now())) {
                throw new CustomException("您的ChatGPT高级权益已过期，请购买高级权益后重试！");
            } else {
                return CarIdUtils.generatorCarID();
            }
        }

        // 根据车号查询session信息
        ChatGptSessionEntity session = getChatGptSessionByCarId(carId);
        if (ObjectUtil.isEmpty(session) && ("plus".equals(type))) {
            // 说明是虚拟车队，找出一个空闲的plus
            session = getIdleCarByPlanType(planType);
        }
        Integer carType = session.getIsPlus();
        // 校验用户套餐的有效期
        // 如果是选的是plus或者team或者pro，则校验plus的过期时间
        if (!PLAN_TYPE_FREE.equals(carType)) {
            if (ObjectUtil.isEmpty(plusExpireTime) || plusExpireTime.isBefore(LocalDateTime.now())) {
                log.error("您的高级权益已过期:{}", plusExpireTime);
                throw new CustomException("您的ChatGPT高级套餐已过期，请购买高级权益后重试");
            }
            // 校验用户是否有使用pro账号的权限
            Integer userType = user.getUserType();
            if (Objects.equals(carType, PLAN_TYPE_PRO) && "pro".equalsIgnoreCase(type) && userType != 4) {
                throw new CustomException("您没有使用Pro账号权限，请联系管理员");
            }
            //如果选择的是4o车队，则需要校验plus时间或者普通时间是否过期
        } else {
            // 优先判断普通的过期时间
            if (user.getExpireTime().isBefore(LocalDateTime.now())) {
                // 普通的也过期则判断plus的过期时间
                if (ObjectUtil.isEmpty(plusExpireTime) || plusExpireTime.isBefore(LocalDateTime.now())) {
                    log.error("普通和高级权益时间都过期了");
                    throw new CustomException("您的ChatGPT基础套餐已过期，请购买基础权益后重试");
                }
            }
        }

        return session.getCarID();
    }

    private ChatGptUserEntity getUserInfoByUserToken(String userToken, String nodeType) {
        // 判断用户token是否有效
        ChatGptUserEntity tokenInfo = "pro".equalsIgnoreCase(nodeType) ? getUserByLoginToken(userToken) : getUserInfoByUsername(userToken);
        if (ObjectUtil.isEmpty(tokenInfo)) {
            log.error("用户不存在：{}",tokenInfo);
            throw new CustomException("用户不存在");
        }
        return tokenInfo;
    }

    private ChatGptSessionEntity getChatGptSessionByCarId(String carId) {
        LambdaQueryWrapper<ChatGptSessionEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatGptSessionEntity::getCarID, carId);
        queryWrapper.eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS);
        queryWrapper.isNull(ChatGptSessionEntity::getDeletedAt);
        return chatGptSessionMapper.selectOne(queryWrapper);
    }

    /**
     * 随机获取一个存在的plus 节点信息
     * @return 实体
     */
    private ChatGptSessionEntity getIdleCarByPlanType(Integer planType) {
        LambdaQueryWrapper<ChatGptSessionEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS);
        queryWrapper.eq(ChatGptSessionEntity::getIsPlus, planType);
        queryWrapper.isNull(ChatGptSessionEntity::getDeletedAt);
        queryWrapper.and(wrapper -> wrapper.lt(ChatGptSessionEntity::getExclusiveExpireTime, LocalDateTime.now())
                .or().isNull(ChatGptSessionEntity::getExclusiveExpireTime));
        queryWrapper.last("ORDER BY RAND() LIMIT 1");
        return chatGptSessionMapper.selectOne(queryWrapper);
    }

    @Override
    public ChatGptUserEntity getUserInfoByUsername(String userToken) {
        return chatGptUserMapper.getUserByUserToken(userToken);
    }

    @Override
    public void changePassword(ChangePasswordDto changePasswordDto) {
        String userName = changePasswordDto.getUserName();
        ChatGptUserEntity entity = chatGptUserMapper.getUserByUserToken(userName);
        if (entity == null) {
            throw new CustomException("用户名查询失败，请检查输入");
        }
        if (entity.getUserType() == USER_TYPE_FREE) {
            throw new CustomException("免费用户不允许修改密码");
        }

        String currentPwd = DigestUtils.md5DigestAsHex(changePasswordDto.getCurrentPassword().getBytes());

        if (!Objects.equals(currentPwd, entity.getPassword())) {
            throw new CustomException("旧密码错误");
        }
        entity.setPassword(DigestUtils.md5DigestAsHex(changePasswordDto.getNewPassword().getBytes()));
        updateByIdWithoutTenant(entity);
        // 删除loginId
        stringRedisTemplate.delete(USER_SESSION_PREFIX + entity.getId());
        // 清理登录锁
        stringRedisTemplate.delete(LOGIN_LOCK + userName);
        stringRedisTemplate.delete(LOGIN_ATTEMPTS + userName);
        stringRedisTemplate.delete(USER_SESSION_PREFIX + entity.getId());
    }

    /**
     * 获取注册验证码
     * @param email 邮箱
     */
    @Override
    public void getRegisterCode(String username, String email) {
        // 获取注册验证码前要先校验用户邮箱和用户名是否已存在
        checkUsernameAndEmailExist(username, email, "false");
        String randomNumbers = RandomUtil.randomNumbers(6);
        contentMap.put("verificationCode", randomNumbers);
        emailService.sendEmail(email, EMAIL_REGISTER_TEMPLATE, EMAIL_SUBJECT, contentMap);
    }

    @Override
    public void getPasswordRestCode(String username, String email) {
        if (StrUtil.isEmpty(username) || StrUtil.isEmpty(email)) {
            throw new CustomException("用户名或邮箱不能为空");
        }

        // 获取注册验证码前要先校验用户邮箱和用户名是否匹配
        Long count = this.lambdaQuery()
                .eq(ChatGptUserEntity::getUserToken, username)
                .eq(ChatGptUserEntity::getEmail, email)
                .isNull(ChatGptUserEntity::getDeletedAt)
                .count();
        if (count < 1) {
            throw new CustomException("当前用户名与绑定的邮箱不匹配");
        }
        String randomNumbers = RandomUtil.randomNumbers(6);
        contentMap.put("verificationCode", randomNumbers);
        emailService.sendEmail(email, EMAIL_REST_PASSWORD_TEMPLATE, EMAIL_SUBJECT, contentMap);

        // 存储重置的邮箱验证码
        stringRedisTemplate.opsForValue().set(email, randomNumbers, 30, TimeUnit.SECONDS);
    }

    @Override
    public void updateExpireTimeForPlusUsers() {
        chatGptUserMapper.updatePlusExpireTime();
    }

    @Override
    public List<String> getIdsByUser(String userName) {
        ChatGptUserEntity entity = this.lambdaQuery()
                .eq(ChatGptUserEntity::getUserToken, userName)
                .isNull(ChatGptUserEntity::getDeletedAt)
                .one();
        if (ObjectUtil.isNotEmpty(entity)  && StrUtil.isNotBlank(entity.getCarids())){
            String carids = entity.getCarids();
            if (StrUtil.isNotBlank(carids)) {
                String[] split = carids.split(",");
                return Arrays.asList(split);
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserDto userDto) {
        // 修改需要判断用户名是否变化，如果变化要查询是否重复
        ChatGptUserEntity user = checkRepeatUsername(userDto);
        List<String> oldIds = dealWithCarIds(user.getCarids());
        Long id = user.getId();
        List<String> ids = userDto.getIds();
        LocalDateTime exclusiveExpireTime = userDto.getExclusiveExpireTime();
        if (!CollectionUtils.isEmpty(ids) && ObjectUtil.isNotEmpty(exclusiveExpireTime)) {
            String collect = String.join(",", ids);
            userDto.setCarids(collect);
            // 先取消再绑定gpt车辆
            if (!CollectionUtils.isEmpty(oldIds)) {
                chatGptSessionMapper.cancelUserGptSessionRelation(id, oldIds);
            }
            chatGptSessionMapper.bindUserGptSession(id, ids, exclusiveExpireTime);
        } else {
            userDto.setCarids(null);
            // 取消绑定gpt车辆
            if (!CollectionUtils.isEmpty(oldIds)) {
                chatGptSessionMapper.cancelUserGptSessionRelation(id, oldIds);
            }
        }
        userDto.setUpdateTime(LocalDateTime.now());
        String password = userDto.getPassword();
        if (StrUtil.isNotBlank(password)) {
            userDto.setPassword(DigestUtils.md5DigestAsHex(password.getBytes()));
            // 如果是管理员角色并且用户名为admin
            updateSysAdminPassword(userDto);
            cleanLoginCache(userDto.getUserToken(), userDto.getId());
        }
        updateById(userDto);
    }

    /**
     * 删除登录锁缓存信息，下线已登录用户
     * @param userToken
     * @param uid
     */
    private void cleanLoginCache(String userToken, Long uid) {
        // 清理登录锁
        stringRedisTemplate.delete(LOGIN_LOCK + userToken);
        stringRedisTemplate.delete(LOGIN_ATTEMPTS + userToken);
        // 删除loginId
        stringRedisTemplate.delete(USER_SESSION_PREFIX + uid);
    }

    private List<String> dealWithCarIds(String carids) {
        return StrUtil.isNotBlank(carids) ? Arrays.asList(carids.split(",")) : new ArrayList<>();
    }


    private ChatGptUserEntity checkRepeatUsername(ChatGptUserEntity user) {
        ChatGptUserEntity entity = getById(user.getId());
        if (ObjectUtil.isEmpty(entity)) {
            throw new CustomException("根据id查询用户名失败，id为：" + user.getId());
        }
        if (!Objects.equals(user.getUserToken(), entity.getUserToken())) {
            ChatGptUserEntity userInfoByUsername = getUserInfoByUsername(user.getUserToken());
            if (ObjectUtil.isNotEmpty(userInfoByUsername)) {
                throw new CustomException("用户名重复");
            }
        }
        return entity;
    }

    @Override
    public IPage<UserDto> getUserOrdersPage(Page<Object> page, String query, String sortProp, String sortOrder) {
        return chatGptUserMapper.selectUserWithInviterPage(page, query, sortProp, sortOrder);
    }

    @Override
    public Map<String, Object> getValidityAndUsage(String username) {
        getClientIp(request);
        log.info("查询使用量的用户信息：{}", username);
        Map<String, Object> map = new HashMap<>();
        ChatGptUserEntity entity = getUserInfoByUsername(username);
        if (ObjectUtil.isNotEmpty(entity)) {
            String usage = stringRedisTemplate.opsForValue().get(USER_GPT_USAGE + entity.getUserToken());
            log.info("缓存中的使用量：{}",usage);

            if (StrUtil.isEmpty(usage)) {
                if (ObjectUtil.isNotEmpty(entity)) {
                    Map<String, ModelLimitDto> mapByUserId = entity.getModelLimits();
                    ModelLimitDto modelLimitDto = mapByUserId.get("gpt-4o");
                    if (modelLimitDto.getLimit() == null || modelLimitDto.getLimit() == 0 || StrUtil.isEmpty(modelLimitDto.getPer())) {
                        usage = "";
                    } else {
                        usage = "剩余次数:" + modelLimitDto.getLimit() + "/" + modelLimitDto.getLimit() + "(" + formatDuration(modelLimitDto.getPer()) + ")";
                    }
                }
            }
            map.put("usage", usage);
            log.info("实际的使用量：{}",usage);
            LocalDateTime validityTime = null;
            if (ObjectUtil.isNotEmpty(entity)) {
                LocalDateTime plusExpireTime = entity.getPlusExpireTime();
                if (ObjectUtil.isNotEmpty(plusExpireTime) && plusExpireTime.isAfter(LocalDateTime.now())) {
                    validityTime =  plusExpireTime;
                } else {
                    validityTime = entity.getExpireTime();
                }
            }
            log.info("会员过期时间：{}",validityTime);
            map.put("validity", formatDateTime(validityTime));
        }
        return map;
    }

    @Override
    public void updateDailyConversationCount(String username, boolean updateClaudeFlag) {
        log.info("更新用户每日使用量、上一次使用时间");
        if (StrUtil.isEmpty(username)) {
            log.error("用户名为空：{}", username);
            throw new CustomException("新增用户每日对话次数失败：用户名为空");
        }
        this.lambdaUpdate().eq(ChatGptUserEntity::getUserToken, username)
                .set(ChatGptUserEntity::getLastActiveTime, LocalDateTime.now())
                .setSql(!updateClaudeFlag, "dailyConversationCount = dailyConversationCount + 1") // 自增 1
                .setSql(updateClaudeFlag, "dailyClaudeConversationCount = dailyClaudeConversationCount + 1") // 自增 1
                .update();
    }

    @Override
    public void updateUserAffQuota(Double money, ChatGptUserEntity user, int orderType) {
        // 先判断是否开启了返现功能和是否开启付费用户才计算返现
        Map<String, String> keyVlaueMap = chatGptConfigService.getKeyValueMapByKeys(List.of(
                "enablePromotion", "enableCashbackForPaidUsers"));
        // 开启了返现功能
        if (Objects.equals(keyVlaueMap.get("enablePromotion"), "true")) {
            Double affRate = user.getAffRate();
            // 根据用户的邀请人id查询邀请人信息
            ChatGptUserEntity inviter = getById(user.getInviterId());
            // 如果没有邀请人信息，则返回。
            if (ObjectUtil.isEmpty(inviter)) {
                log.info("没有邀请人：{}，不计算返现", inviter);
                return;
            }
            if (Objects.equals(keyVlaueMap.get("enableCashbackForPaidUsers"), "true")
                    && inviter.getUserType() != USER_TYPE_ADVANCE) {
                log.warn("管理员开启了邀请人未付费用户不能享有返现福利");
                return;
            }

            // 推广次数加1
            inviter.setAffCount((inviter.getAffCount() == null ? 0 : inviter.getAffCount()) + 1);

            // 新增邀请人的邀请记录
            addAffRecord(money, user, orderType);

            // 返现比例合理，则计算返现
            boolean valid = isCashbackRateValid(affRate);
            if (valid) {
                // 累计可提现金额
                BigDecimal moneyDecimal = BigDecimal.valueOf(money);
                BigDecimal affRateDecimal = BigDecimal.valueOf(affRate);
                BigDecimal affMoneyDecimal = moneyDecimal.multiply(affRateDecimal);
                String affQuota = inviter.getAffQuota();
                BigDecimal affQuotaDecimal = BigDecimal.valueOf(StrUtil.isEmpty(affQuota) ? 0.0 : Double.parseDouble(affQuota));
                BigDecimal finalAffQuotaDecimal = affQuotaDecimal.add(affMoneyDecimal);
                inviter.setAffQuota(String.valueOf(finalAffQuotaDecimal.setScale(2, RoundingMode.HALF_UP)));
                // 累积总推广金额
                String affTotalQuota = inviter.getAffTotalQuota();
                BigDecimal totalQuotaDecimal = BigDecimal.valueOf(StrUtil.isEmpty(affTotalQuota) ? 0.0 : Double.parseDouble(affTotalQuota));
                inviter.setAffTotalQuota(totalQuotaDecimal.add(affMoneyDecimal).toString());
                updateByIdWithoutTenant(inviter);
            } else {
                throw new CustomException("返现比例设置不合理，请在0.1-1.0之间");
            }
        }
    }

    /**
     * 新增邀请人的邀请记录
     * @param money
     * @param user
     * @param orderType
     */
    private void addAffRecord(Double money, ChatGptUserEntity user, int orderType) {
        ChatGptAffRecordEntity record = new ChatGptAffRecordEntity();
        record.setAffMoney(money);
        record.setInviteeId(user.getId());
        record.setInviterId(user.getInviterId());
        record.setOrderType(orderType);
        chatGptAffRecordMapper.insert(record);
    }

    @Override
    public void compensateTime(String type, Long minutes) {
        log.info("补偿用户时长,type:{},minutes:{}", type, minutes);
        if (StrUtil.isEmpty(type) || ObjectUtil.isEmpty(minutes)) {
            throw new CustomException("补偿时间和类型为空");
        }
        switch (type) {
            case "1":
                this.lambdaUpdate()
                        .isNotNull(ChatGptUserEntity::getExpireTime)
                        .ne(ChatGptUserEntity::getIsAdmin, 1)
                        .setSql("expireTime = DATE_ADD(expireTime, INTERVAL " + minutes + " MINUTE)")
                        .update();
                break;
            case "2":
                this.lambdaUpdate()
                        .isNotNull(ChatGptUserEntity::getExpireTime)
                        .isNotNull(ChatGptUserEntity::getPlusExpireTime)
                        .ne(ChatGptUserEntity::getIsAdmin, 1)
                        .setSql("expireTime = DATE_ADD(expireTime, INTERVAL " + minutes + " MINUTE), " +
                                "plusExpireTime = DATE_ADD(plusExpireTime, INTERVAL " + minutes + " MINUTE)")
                        .update();
                break;
            case "3":
                this.lambdaUpdate()
                        .isNotNull(ChatGptUserEntity::getClaudeExpireTime)
                        .ne(ChatGptUserEntity::getIsAdmin, 1)
                        .setSql("claudeExpireTime = DATE_ADD(claudeExpireTime, INTERVAL " + minutes + " MINUTE)")
                        .update();
                break;
            case "4":
                this.lambdaUpdate()
                        .isNotNull(ChatGptUserEntity::getClaudeProExpireTime)
                        .ne(ChatGptUserEntity::getIsAdmin, 1)
                        .setSql("claudeExpireTime = DATE_ADD(claudeExpireTime, INTERVAL " + minutes + " MINUTE)," +
                                "claudeProExpireTime = DATE_ADD(claudeProExpireTime, INTERVAL " + minutes + " MINUTE)")
                        .update();
                break;
            case "5":
                this.lambdaUpdate()
                        .isNotNull(ChatGptUserEntity::getGrokExpireTime)
                        .ne(ChatGptUserEntity::getIsAdmin, 1)
                        .setSql("grokExpireTime = DATE_ADD(grokExpireTime, INTERVAL " + minutes + " MINUTE)")
                        .update();
                break;
            case "6":
                this.lambdaUpdate()
                        .isNotNull(ChatGptUserEntity::getGrokSuperExpireTime)
                        .ne(ChatGptUserEntity::getIsAdmin, 1)
                        .setSql("grokExpireTime = DATE_ADD(grokExpireTime, INTERVAL " + minutes + " MINUTE)," +
                                "grokSuperExpireTime = DATE_ADD(grokSuperExpireTime, INTERVAL " + minutes + " MINUTE)")
                        .update();
                break;
            default:
                throw new CustomException("补偿类型错误");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ChatGptUserEntity> batchCreateUser(BatchUserDto dto) {
        List<ChatGptUserEntity> userEntities = new ArrayList<>();
        // 获取配置信息
        Map<String, String> map = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("affRate", "modelLimits", "drawRegisterCount"));
        // 查询套餐信息是否存在
        Long subtypeId = dto.getSubtypeId();
        if (ObjectUtil.isNotEmpty(subtypeId)) {
            ChatGptSubTypeEntity subType = chatGptSubTypeService.getById(subtypeId);
            if (ObjectUtil.isEmpty(subType)) {
                throw new CustomException("套餐不存在");
            }
        }
        for (int i = 0; i < dto.getNums(); i++) {
            ChatGptUserEntity entity = new ChatGptUserEntity();
            // 创建授权码登录用户
            if (dto.getLoginType() == LoginType.CODE.getType()) {
                entity.setUserToken(IdUtil.randomUUID());
                entity.setPassword(DigestUtils.md5DigestAsHex("123456".getBytes()));
                entity.setLoginType(dto.getLoginType());
            } else {
                String password = dto.getPassword();
                entity.setPassword(StrUtil.isNotBlank(password)? DigestUtils.md5DigestAsHex(password.getBytes()): DigestUtils.md5DigestAsHex("123456".getBytes()));
                String prefix = dto.getTokenPrefix();
                entity.setUserToken(StrUtil.isNotBlank(prefix)? prefix+"_" + RandomUtil.randomString(8): RandomUtil.randomString(8));
            }
            entity.setExpireTime(LocalDateTime.now());
            entity.setSubTypeId(ObjectUtil.isNotEmpty(subtypeId)? subtypeId.intValue() : null);
            entity.setAffCode(RandomUtil.randomStringUpper(6));
            entity.setRemark(dto.getRemark());
            entity.setAffRate(dto.getAffRate());
            entity.setModelLimits(getSysModelLimitMap(map.get("modelLimits")));
            entity.setAffRate(Optional.ofNullable(map.get("affRate")).map(Double::parseDouble).orElse(0.0));
            entity.setLoginToken(IdUtil.simpleUUID()); // 生成认证token
            userEntities.add(entity);
        }
        saveBatch(userEntities);

        // 新增绘画额度数据
        List<Long> ids = userEntities.stream().map(ChatGptUserEntity::getId).toList();
        long count = Optional.ofNullable(map.get("drawRegisterCount")).map(Long::parseLong).orElse(0L);
        drawingQuotaService.insertDrawQuota(ids, count);
        return userEntities;
    }

    @Override
    public Map<String, String> getAccessTokenAndCarId() {
        Map<String, String> map = new HashMap<>();
        String visitorUsePlus = chatGptConfigService.getValueByKey("visitorUsePlus");
        Integer planType = "true".equals(visitorUsePlus) ? PLAN_TYPE_PLUS : PLAN_TYPE_FREE;
        ChatGptSessionEntity session = getIdleCarByPlanType(planType);
        if (ObjectUtil.isNotEmpty(session)) {
            map.put("carId", session.getCarID());
        }
        return map;
    }

    @Override
    public void updateUserEmailByUserId(String userId, String email) {
        log.info("开始绑定用户邮箱...");
        ChatGptUserEntity user = getById(userId);
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户信息不存在");
        }
        Long count = this.lambdaQuery().eq(ChatGptUserEntity::getEmail, email).count();
        if (count.intValue() > 0) {
            throw new CustomException("邮箱已存在");
        }
        user.setEmail(email);
        updateByIdWithoutTenant(user);
        log.info("绑定用户邮箱成功");
    }

    /**
     * 检查用户是否有备用节点的权限
     * @param userId
     */
    @Override
    public boolean checkBackAccess(String userId) {
        ChatGptUserEntity entity = this.getByIdWithoutTenant(Long.parseLong(userId));
        if (ObjectUtil.isEmpty(entity)) {
            throw new CustomException("用户信息不存在");
        }
        String useBackNode = chatGptConfigService.getValueByKey("useBackNode");
        if (!Objects.equals("true", useBackNode)) {
            LocalDateTime plusExpireTime = entity.getPlusExpireTime();
            LocalDateTime claudeExpireTime = entity.getClaudeExpireTime();
            LocalDateTime proExpireTime = entity.getClaudeProExpireTime();
            boolean isPlusMemberValid = isNotExpired(plusExpireTime);
            boolean isClaudeMemberValid = isNotExpired(claudeExpireTime);
            boolean isClaudeProMemberValid = isNotExpired(proExpireTime);
            if (!isPlusMemberValid && !isClaudeMemberValid && !isClaudeProMemberValid) {
                throw new CustomException("您的权益已过期，无法使用备用节点，请先充值后重试");
            }
        }
        return true;
    }

    @Override
    public List<UserPayLogsVo> getPaymentHistory(String userToken) {
        return chatGptUserMapper.listPaymentHistory(userToken);
    }

    @Override
    public void updateBatchUserExpireTimeTimeForPlusUsers() {
        chatGptUserMapper.updateBatchUserExpireTime();
    }


    @Override
    public void logout(HttpServletRequest request, String username) {
        String currentSessionId = getCookieValue(request, "gfsessionid");
        stringRedisTemplate.opsForSet().remove(USER_SESSIONS + username, currentSessionId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SignInRecordEntity signIn(Long userId) {
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        // 根据用户id和日期查询今日是否已经签到
        checkSignInStatus(userId, today);
        // 增加用户奖励
        updateUserExpireTime(userId);
        // 创建签到记录
        return saveSignInRecord(userId, today);
    }

    private void updateUserExpireTime(Long userId) {
        log.info("增加用户签到奖励");
        ChatGptUserEntity user = getById(userId);
        Map<String, String> keyVlaueMap = chatGptConfigService.getKeyValueMapByKeys(List.of("enableSignIn", "signInTime", "signInType", "enablePlusSignIn", "enableFreeSignIn"));
        boolean enableSignIn = Optional.ofNullable(keyVlaueMap.get("enableSignIn"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .map(Boolean::valueOf)
                .orElse(false);
        log.info("enableSignIn:{}",enableSignIn);
        String signInTime = Optional.ofNullable(keyVlaueMap.get("signInTime"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .orElse("0");
        log.info("signInTime:{}",signInTime);
        String signInType = Optional.ofNullable(keyVlaueMap.get("signInType"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .orElse("1");
        log.info("signInType:{}",signInType);
        boolean enablePlusSignIn = Optional.ofNullable(keyVlaueMap.get("enablePlusSignIn"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .map(Boolean::valueOf)
                .orElse(false);
        log.info("enablePlusSignIn:{}",enablePlusSignIn);
        boolean enableFreeSignIn = Optional.ofNullable(keyVlaueMap.get("enableFreeSignIn"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .map(Boolean::valueOf)
                .orElse(false);
        log.info("enableFreeSignIn:{}",enableFreeSignIn);
        long signInTimeLong = Long.parseLong(signInTime);
        boolean sendPlus = enablePlusSignIn
                && (enableFreeSignIn || user.getUserType() == USER_TYPE_ADVANCE);
        if (ObjectUtil.isNotEmpty(user) && enableSignIn) {
            switch (signInType) {
                case "1":
                    if (sendPlus) {
                        user.setPlusExpireTime(getValidTime(user.getPlusExpireTime()).plusMinutes(signInTimeLong));
                    }
                    user.setExpireTime(getValidTime(user.getExpireTime()).plusMinutes(signInTimeLong));
                    break;
                case "2":
                    if (sendPlus) {
                        user.setPlusExpireTime(getValidTime(user.getPlusExpireTime()).plusMinutes(signInTimeLong));
                        user.setClaudeProExpireTime(getValidTime(user.getClaudeProExpireTime()).plusMinutes(signInTimeLong));
                    }
                    user.setExpireTime(getValidTime(user.getExpireTime()).plusMinutes(signInTimeLong));
                    user.setClaudeExpireTime(getValidTime(user.getClaudeExpireTime()).plusMinutes(signInTimeLong));
                    break;
                case "3":
                    if (sendPlus) {
                        user.setClaudeProExpireTime(getValidTime(user.getClaudeProExpireTime()).plusMinutes(signInTimeLong));
                    }
                    user.setClaudeExpireTime(getValidTime(user.getClaudeExpireTime()).plusMinutes(signInTimeLong));
                    break;
                case "4":
                    if (sendPlus) {
                        user.setGrokSuperExpireTime(getValidTime(user.getGrokSuperExpireTime()).plusMinutes(signInTimeLong));
                    }
                    user.setGrokExpireTime(getValidTime(user.getGrokExpireTime()).plusMinutes(signInTimeLong));

                    break;
                default:
                    throw new CustomException("签到类型错误");
            }
            updateByIdWithoutTenant(user);
        }
    }

    private LocalDateTime getValidTime(LocalDateTime expireTime) {
        return Optional.ofNullable(expireTime).filter(e -> LocalDateTime.now().isBefore(e)).orElse(LocalDateTime.now());
    }

    private void checkSignInStatus(Long userId, LocalDate today) {
        boolean hasSignedIn = signInRecordsMapper.exists(
                new LambdaQueryWrapper<SignInRecordEntity>()
                        .eq(SignInRecordEntity::getSignedDate, today)
                        .eq(SignInRecordEntity::getUserId, userId));
        if (hasSignedIn) {
            throw new CustomException("今日已签到");
        }
    }

    private SignInRecordEntity saveSignInRecord(Long userId, LocalDate today) {
        SignInRecordEntity record = new SignInRecordEntity();
        record.setUserId(userId);
        record.setSignedDate(today);
        signInRecordsMapper.insert(record);
        return record;
    }

    @Override
    public SignInInfoDTO getSignInInfo(Long userId) {
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        List<SignInRecordEntity> monthRecords = signInRecordsMapper.selectList(
                new LambdaQueryWrapper<SignInRecordEntity>()
                        .between(SignInRecordEntity::getSignedDate, startOfMonth, endOfMonth)
                        .eq(SignInRecordEntity::getUserId, userId)
                        .orderByAsc(SignInRecordEntity::getSignedDate)

        );
        // 检查今日是否已签到
        boolean hasSignedIn = signInRecordsMapper.exists(
                new LambdaQueryWrapper<SignInRecordEntity>()
                        .eq(SignInRecordEntity::getUserId, userId)
                        .eq(SignInRecordEntity::getSignedDate, today)
        );
        SignInInfoDTO info = new SignInInfoDTO();
        info.setHasSignedIn(hasSignedIn);
        info.setHasSignedIn(hasSignedIn);
        info.setSignedDates(monthRecords.stream()
                .map(record -> record.getSignedDate().getDayOfMonth())
                .collect(Collectors.toList()));
        return info;
    }

    @Override
    public UserInfoVo getUserInfo(String userName) {
        ChatGptUserEntity user = getUserInfoByUsername(userName);
        UserInfoVo userInfo = new UserInfoVo();
        BeanUtil.copyProperties(user, userInfo);
        return userInfo;
    }

    @Override
    public IPage<UserDto> selectPage(Integer page, Integer size, String query, String sortOrder, String sortProp) {
        // 执行分页查询
        IPage<UserDto> pageInfo = this.getUserOrdersPage(new Page<>(page,size), query, sortProp, sortOrder);

        List<UserDto> records = pageInfo.getRecords();

        List<UserDto> dtoList = records.stream()
                .peek(dto -> {
                    if (StrUtil.isNotEmpty(dto.getCarids())) {
                        dto.setIds(Arrays.asList(dto.getCarids().split(",")));
                    }
                }).collect(Collectors.toList());
        // 返回分页查询结果
        IPage<UserDto> userDtoIPage = new Page<>();
        userDtoIPage.setTotal(pageInfo.getTotal());
        userDtoIPage.setCurrent(pageInfo.getCurrent());
        userDtoIPage.setSize(pageInfo.getSize());
        userDtoIPage.setRecords(dtoList);
        return userDtoIPage;
    }

    @Override
    public Map<String, ModelLimitDto> getUserLimits(Long id) {
        ChatGptUserEntity byId = chatGptUserMapper.getByIdWithoutTenant(id);
        if(ObjectUtil.isEmpty(byId)) {
            return Collections.emptyMap();
        }
        return byId.getModelLimits();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUserInfoAndPayStatus(ChatGptSubTypeEntity subType,
                                          ChatGptUserEntity user,
                                          ChatGptEPayLogsEntity payLogsEntity) {
        // 计算用户的过期时间和模型速率
        calculateExpirationTime(subType, user);

        // 更新用户权益信息
        updateByIdWithoutTenant(user);

        // 更新用户的返现信息
        updateUserAffQuota(payLogsEntity.getMoney(), user, AFF_RECORD_TYPE_RECHARGE);

        // 处理租户的佣金余额
        tenantService.calcTenantBalance(user, payLogsEntity.getMoney());

        // 更新订单状态
        payLogsEntity.setStatus("success");
        return chatGptPayLogsMapper.updateById(payLogsEntity);
    }

    @Override
    public Map<String, Object> authCode(String code) {
        ChatGptUserEntity user = findUserByAuthCode(code);
        if (ObjectUtil.isNotEmpty(user)) {
            // 判断用户是否有套餐id并且是首次登录，则权益开始生效。
            Integer subTypeId = user.getSubTypeId();
            if (subTypeId > 0 && ObjectUtil.isEmpty(user.getLastActiveTime())) {
                log.info("批量生成的用户首次登录，权益开始生效");
                ChatGptSubTypeEntity subType = chatGptSubTypeMapper.getSubtypeByIdWithoutTenant(String.valueOf(subTypeId));
                calculateExpirationTime(subType, user);
                // 根据套餐更新模型速率信息
                user.setModelLimits(subType.getModelLimits());
            }
            String clientIp = getClientIp(request);
            user.setLastActiveTime(LocalDateTime.now());
            user.setClientIp(clientIp);
            this.updateByIdWithoutTenant(user);

            Map<String, Object> map = new HashMap<>();
            UserInfoVo userInfoVo = new UserInfoVo();
            BeanUtil.copyProperties(user, userInfoVo);
            // 生成jwt
            String jwtToken = JwtUtil.create(user.getId(), user.getUserToken(), user.getIsAdmin());
            map.put("user", userInfoVo);
            map.put("token", jwtToken);
            return map;
        } else {
            throw new CustomException("授权码不存在，请检查");
        }
    }

    @Override
    public void dealUserAndGptSessionRelation(ChatGptSubTypeEntity subType, ChatGptUserEntity user) {
        if (subType.getExclusive() == 1 && Objects.equals(subType.getSubType(), "1")) {
            // 查询一个绑定事件已经过期的gpt session，将其绑定上
            ChatGptSessionEntity session = chatGptSessionMapper.selectOne(new LambdaQueryWrapper<ChatGptSessionEntity>()
                    .and(wrapper -> wrapper.lt(ChatGptSessionEntity::getExclusiveExpireTime, LocalDateTime.now()).or().isNull(ChatGptSessionEntity::getExclusiveExpireTime))
                    .isNull(ChatGptSessionEntity::getUserId)
                    .eq(ChatGptSessionEntity::getIsPlus, subType.getExclusiveType())
                    .eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS)
                    .isNull(ChatGptSessionEntity::getDeletedAt)
                    .last("limit 1"));
            if (ObjectUtil.isEmpty(session)) {
                log.error("当前gpt账号列表都已经被绑定完，请重新添加账号");
                throw new CustomException("当前暂没有可用的独享账号，请联系管理员上架");
            }
            // 绑定用户的gpt账号
            chatGptSessionMapper.bindUserGptSession(
                    user.getId(),
                    Collections.singletonList(String.valueOf(session.getId())),
                    LocalDateTime.now().plusDays(subType.getValidDays()));
            String carids = user.getCarids();
            if (StrUtil.isNotEmpty(carids)) {
                carids = carids + "," + session.getId();
            } else {
                carids = String.valueOf(session.getId());
            }
            user.setCarids(carids);
        }
    }

    @Override
    public String getSassLoginToken(HttpServletRequest request) {
        log.info("获取sass登陆地址");
        String token = request.getHeader(AUTH_HEADER);
        Long uid = JwtUtil.getUid(token);
        if (ObjectUtil.isEmpty(uid)) {
            throw new ServiceException(401, "登录过期,请重新登录");
        }
        ChatGptUserEntity user = this.getByIdWithoutTenant(uid);
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户信息获取失败，请重新登录后尝试");
        }
        checkSassAccess(user);
        String sxUrl = chatGptConfigService.getValueByKey("soruxGptSideBarUrl");
        if (StrUtil.isEmpty(sxUrl)) {
            log.warn("soruxgpt 地址未配置");
            throw new CustomException("管理员未配置url");
        }
        return String.format("/codetoken?logintoken=%s", user.getLoginToken());
    }

    /**
     * 校验使用sass的权限：支用plus以上的用户才可以使用
     * @param user
     */
    private void checkSassAccess(ChatGptUserEntity user) {
        LocalDateTime plusExpireTime = user.getPlusExpireTime();
        if (ObjectUtil.isEmpty(plusExpireTime) ) {
            throw new CustomException("您还没有购买套餐，请购买高级套餐后继续使用。");
        }
        if (plusExpireTime.isBefore(LocalDateTime.now())) {
            throw new CustomException("您的高级权益已到期，请充值后重试");
        }
    }

    @Override
    public String getGrokLoginToken(HttpServletRequest request, Integer isSuper) {
        String token = request.getHeader(AUTH_HEADER);
        Long uid = JwtUtil.getUid(token);
        if (ObjectUtil.isEmpty(uid)) {
            throw new ServiceException(401, "登录过期,请重新登录");
        }
        ChatGptUserEntity user = this.getByIdWithoutTenant(uid);
        checkGrokAccess(user, isSuper);
        String grokUrl = chatGptConfigService.getValueByKey("grokUrl");
        if (StrUtil.isEmpty(grokUrl)) {
            log.error("grok 地址未配置");
            throw new CustomException("管理员未配置 grok 地址");
        }
        return String.format("/signtoken?usertoken=%s&isSuper=%s", user.getLoginToken(), isSuper);
    }

    /**
     * 未过期用户可用
     * @param user
     */
    private static Map<String, Object> checkGrokAccess(ChatGptUserEntity user, Integer isSuper) {
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户数据为空");
        }
        LocalDateTime grokExpireTime = user.getGrokExpireTime();
        LocalDateTime superExpireTime = user.getGrokSuperExpireTime();
        LocalDateTime now = LocalDateTime.now();
        // 如果是super，则要校验super过期时间
        boolean isSuperPlan = isSuper == 1;
        if (isSuperPlan) {
            if (ObjectUtil.isNotEmpty(superExpireTime) && superExpireTime.isAfter(now)) {
                return Map.of("expireTime", superExpireTime, "isPro", true);
            } else {
                throw new CustomException("grok Super 使用权益已经过期，请回到首页充值后重试。");
            }
        } else {
            if (ObjectUtil.isNotEmpty(grokExpireTime) && grokExpireTime.isAfter(now)) {
                return Map.of("expireTime", grokExpireTime, "isPro", false);
            }
            if(ObjectUtil.isNotEmpty(superExpireTime) && superExpireTime.isAfter(now)) {
                return Map.of("expireTime", superExpireTime, "isPro", false);
            }
            throw new CustomException("grok 使用权益已经过期，请回到首页充值后重试。");
        }
    }

    @Override
    public Map<String, Object> authGrokAccess(String loginToken, Integer isSuper) {
        log.info("收到 grok :{} 登录认证请求", loginToken);
        if (!licenseValidator.haveAccess("grok")) {
            throw new CustomException("sass grok未授权，请联系管理员");
        }
        ChatGptUserEntity user = getUserByLoginToken(loginToken);
        Map<String, Object> map = checkGrokAccess(user, isSuper);
        return Map.of("code", 1, "expireTime", map.get("expireTime"), "isPro", map.get("isPro"), "msg","请求成功");
    }

    @Override
    public List<CarInfoVo> getGrokCarPage() {
        List<CarInfoVo> carList = new ArrayList<>();
        Map<String, String> map = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("grokNum", "grokName",
        "grokSuperNum", "grokSuperName"));
        int grokNum = Optional.ofNullable(map.get("grokNum")).map(Integer::parseInt).orElse(20);
        int grokSuperNum = Optional.ofNullable(map.get("grokSuperNum")).map(Integer::parseInt).orElse(20);
        String grokName = map.get("grokName");
        String grokSuperName = map.get("grokSuperName");
        if (grokNum > 0 || grokSuperNum > 0) {
            // 如果缓存中已经有车号列表并且与设置的虚拟数量一致，直接返回
            if (cachedGrokCarList.size() == grokNum + grokSuperNum) {
                carList.addAll(cachedGrokCarList);
                return carList;
            }
            cachedGrokCarList.clear();  // 如果虚拟数量不同，清空缓存列表
            grokVirtualCars(grokNum, grokName, carList, false);
            grokVirtualCars(grokSuperNum, grokSuperName, carList, true);
        }
        return carList;
    }

    private void grokVirtualCars(int grokNum, String grokName, List<CarInfoVo> carList, boolean isSuper) {
        Random random = new Random();
        for (int i = 0; i < grokNum; i++) {
            CarInfoVo carInfoVo = new CarInfoVo();
            carInfoVo.setCarID(getVirtualCarName(i, grokName));
            carInfoVo.setIsPlus(isSuper? 1 : 0);
            CarStatus carStatus = new CarStatus();
            carStatus.setIsPlus(isSuper? "1" : "0");
            String randomStatus = isFreeTime() ? status.get(0) : status.get(random.nextInt(status.size()));
            int count;
            if ("空闲".equals(randomStatus)) {
                count = random.nextInt(60); // 空闲状态下 count 范围是 0 到 19
            } else {
                count = 60 + random.nextInt(60); // 繁忙状态下 count 范围是 20 到 39
            }
            // 凌晨清空虚拟车的次数
            if (isFreeTime()) {
                count = 0;
            }
            carStatus.setCount(count);
            carInfoVo.setCount((long) count);
            carInfoVo.setStatus(randomStatus);
            carInfoVo.setDetail("空闲".equals(randomStatus)? "推荐": "可用");
            carInfoVo.setCarStatus(carStatus);
            carList.add(carInfoVo);
            cachedGrokCarList.add(carInfoVo);
        }
    }

    @Override
    public ChatGptUserEntity getUserByLoginToken(String loginToken) {
        return chatGptUserMapper.getUserByLoginToken(loginToken);
    }

    @Override
    public List<CarInfoVo> getSassCarPage() {
        List<CarInfoVo> carList = new ArrayList<>();
        Map<String, String> map = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("sassNum", "sassName"));
        int sassNum = Optional.ofNullable(map.get("sassNum")).map(Integer::parseInt).orElse(20);
        String sassName = map.get("sassName");
        if (sassNum > 0) {
            // 如果缓存中已经有车号列表并且与设置的虚拟数量一致，直接返回
            if (cachedSassCarList.size() == sassNum) {
                carList.addAll(cachedSassCarList);
                return carList;
            }
            cachedSassCarList.clear();  // 如果虚拟数量不同，清空缓存列表
            Random random = new Random();
            for (int i = 0; i < sassNum; i++) {
                CarInfoVo carInfoVo = new CarInfoVo();
                carInfoVo.setCarID(getVirtualCarName(i, sassName));
                carInfoVo.setIsPlus(2);
                CarStatus carStatus = new CarStatus();
                carStatus.setIsPlus("1");
                String randomStatus = isFreeTime() ? status.get(0) : status.get(random.nextInt(status.size()));
                int count;
                if ("空闲".equals(randomStatus)) {
                    count = random.nextInt(60); // 空闲状态下 count 范围是 0 到 19
                } else {
                    count = 60 + random.nextInt(60); // 繁忙状态下 count 范围是 20 到 39
                }
                // 凌晨清空虚拟车的次数
                if (isFreeTime()) {
                    count = 0;
                }
                carStatus.setCount(count);
                carInfoVo.setCount((long) count);
                carInfoVo.setStatus(randomStatus);
                carInfoVo.setDetail("空闲".equals(randomStatus)? "推荐": "可用");
                carInfoVo.setCarStatus(carStatus);
                carList.add(carInfoVo);
                cachedSassCarList.add(carInfoVo);
            }
        }
        return carList;
    }

    @Override
    public ChatGptUserEntity getByIdWithoutTenant(long userId) {
        return chatGptUserMapper.getByIdWithoutTenant(userId);
    }

    @Override
    public void updateByIdWithoutTenant(ChatGptUserEntity user) {
        chatGptUserMapper.updateByIdWithoutTenant(user);
    }

    @Override
    public void resetPwd(Long id) {
        if (id == null) {
            return;
        }
        // 判断用户是否存在
        ChatGptUserEntity user = getByIdWithoutTenant(id);
        String newPwd = DigestUtils.md5DigestAsHex("123456".getBytes());
        this.lambdaUpdate().eq(ChatGptUserEntity::getId, id).set(ChatGptUserEntity::getPassword, newPwd).update();
        // 清理登录锁
        cleanLoginCache(user.getUserToken(), user.getId());
    }

    @Override
    public Map<String, String> getLoginToken(HttpServletRequest request) {
        Long uid = getUid(request);
        String loginToken = chatGptUserMapper.getLoginTokenById(uid);
        return Map.of("loginToken", loginToken);
    }

    private ChatGptUserEntity findUserByAuthCode(String code) {
        return chatGptUserMapper.selectOneByNameAndLoginType(code, LoginType.CODE.getType());
    }

    private boolean isNotExpired(LocalDateTime expireTime) {
        LocalDateTime now = LocalDateTime.now();
        return ObjectUtil.isNotEmpty(expireTime) && expireTime.isAfter(now);
    }

    /**
     * 校验返现比例是否合法
     * @param affRate 返现比例
     */
    private boolean isCashbackRateValid(Double affRate) {
        return affRate != null && affRate >= 0.01 && affRate <= 1.0;
    }
    /**
     * 更新系统管理员密码
     * @param user 用户
     */

    private void updateSysAdminPassword(ChatGptUserEntity user) {
        if (user.getIsAdmin() == 1 && "admin".equals(user.getUserToken())){
            UpdateWrapper<BaseSysUserEntity> wrapper = new UpdateWrapper<>();
            wrapper.set("password", user.getPassword());
            wrapper.eq("username", user.getUserToken());
            baseSysUserMapper.update(wrapper);
        }
    }

    /**
     * 批量更新删除时间
     * @param ids 用户ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeBatchByIds(List<Long> ids) {
        // 过滤管理员
        List<Long> filteredList = ids.stream().filter(e -> e!=999999999).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filteredList)) {
            chatGptUserMapper.updateIsDeletedByIds(filteredList);
        }
    }

    @Override
    public void updateStatusById(Long id, Integer isPlus) {
        ChatGptUserEntity token = chatGptUserMapper.selectById(id);
        if (ObjectUtil.isEmpty(token)) {
            throw new CustomException("用户信息不存在");
        }
        token.setIsPlus(isPlus);
        chatGptUserMapper.updateById(token);
    }

    @Override
    public StatisticVo getStatisticData() {
        // 计算当日新增用户数、在线数、总用户数
        Map<String, Long> userStats = chatGptUserMapper.calculateUserStats(LocalDateTime.now().minusHours(24).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        Long onlineUsers = userStats.get("onlineUsers");
        Long newUsers = userStats.get("newUsers");
        Long totalUsers = userStats.get("totalUsers");
        // 统计支付信息
        Map<String, Object> overallStats = chatGptPayLogsMapper.getOverallPayStats();
        // 获取具体值
        double todayTotalMoney = 0.0;
        long todayPayNum = 0;
        long todayUnPayNum = 0;
        double monthTotalMoney = 0.0;
        if (overallStats != null) {
            todayTotalMoney = overallStats.get("todayTotalMoney") instanceof Number
                    ? ((Number) overallStats.get("todayTotalMoney")).doubleValue()
                    : 0.0;
            todayPayNum = overallStats.get("todayPayNum") instanceof Number
                    ? ((Number) overallStats.get("todayPayNum")).longValue()
                    : 0L;
            todayUnPayNum = overallStats.get("todayUnPayNum") instanceof Number
                    ? ((Number) overallStats.get("todayUnPayNum")).longValue()
                    : 0L;
            monthTotalMoney = overallStats.get("monthTotalMoney") instanceof Number
                    ? ((Number) overallStats.get("monthTotalMoney")).doubleValue()
                    : 0.0;
        }
        // 查询当前租户未结算佣金余额
        String tenantId = SecurityUtil.getTenantId();
        Double unsettledCommission = sysTenantService.getUnsettledCommission(tenantId);
        // 统计话题数
        LambdaQueryWrapper<ChatGptConversationEntity> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime startTime = LocalDate.now().atStartOfDay();
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        wrapper.between(ChatGptConversationEntity::getCreateTime,startTime, endTime);
        Long topics = conversationsDao.selectCount(wrapper);

        // 统计模型对话使用量
        String todayNormalUsage = stringRedisTemplate.opsForValue().get(TODAY_NORMAL_USAGE);
        String todayAdvanceUsage = stringRedisTemplate.opsForValue().get(TODAY_ADVANCE_USAGE);
        String todayClaudeUsage = stringRedisTemplate.opsForValue().get(TODAY_CLAUDE_USAGE);
        String todayVisitorUsage = stringRedisTemplate.opsForValue().get(TODAY_VISITOR_USAGE);
        return StatisticVo.builder()
                .addNum(newUsers)
                .unPayNum(todayUnPayNum)
                .payNum(todayPayNum)
                .totalNum(totalUsers)
                .totalMoney(todayTotalMoney)
                .monthTotalMoney(monthTotalMoney)
                .unsettledCommission(unsettledCommission)
                .topics(topics)
                .onlineNums(onlineUsers.intValue())
                .normalUsage(StrUtil.isEmpty(todayNormalUsage) ? 0L : Long.parseLong(todayNormalUsage))
                .advanceUsage(StrUtil.isEmpty(todayAdvanceUsage) ? 0L : Long.parseLong(todayAdvanceUsage))
                .claudeUsage(StrUtil.isEmpty(todayClaudeUsage) ? 0L : Long.parseLong(todayClaudeUsage))
                .visitorUsage(StrUtil.isEmpty(todayVisitorUsage) ? 0L : Long.parseLong(todayVisitorUsage))
                .build();
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void verifyCodeAndSaveUserInfo(RegisterDto registerDto) {
        Map<String, String> keyVlaueMap = chatGptConfigService.getKeyValueMapByKeys(
                List.of("freeTime", "affTime", "enablePlus", "enableEmailCheck","enableInviteCode",
                        "enableRepeatRegister", "affRate", "modelLimits", "drawRegisterCount"));
        String freeTime = keyVlaueMap.get("freeTime");
        String affTime = keyVlaueMap.get("affTime");
        String enablePlus = keyVlaueMap.get("enablePlus");
        String modelLimits = keyVlaueMap.get("modelLimits");
        String enableEmailCheck = keyVlaueMap.getOrDefault("enableEmailCheck", "true");
        String enableInviteCode = keyVlaueMap.get("enableInviteCode");
        String enableRepeatRegister = keyVlaueMap.get("enableRepeatRegister");
        Double affRate = StrUtil.isEmpty(keyVlaueMap.get("affRate"))? 0.0 : Double.parseDouble(keyVlaueMap.get("affRate"));
        if (Objects.equals(enableEmailCheck, "true")){
            validRegisterCode(registerDto.getEmail(), registerDto.getCode());
            // 验证成功则删除缓存
            stringRedisTemplate.delete(VERIFICATION_CODE_PREFIX + registerDto.getEmail());
        }
        // 检查新增的用户名或者邮箱是否存在
        checkUsernameAndEmailExist(registerDto.getUsername(), registerDto.getEmail(), enableRepeatRegister);
        // 如果填写了邀请码，则给邀请人增加体验时长
        ChatGptUserEntity inviter = updateInviterExpire(registerDto, affTime, enablePlus, enableInviteCode);
        // 新增用户信息
        ChatGptUserEntity user = saveNewUser(registerDto, freeTime, enablePlus, inviter, affRate, modelLimits);
        // 新增绘画额度数据
        long count = Optional.ofNullable(keyVlaueMap.get("drawRegisterCount")).map(Long::parseLong).orElse(0L);
        drawingQuotaService.insertDrawQuota(Collections.singletonList(user.getId()), count);
    }

    /**
     * 判断用户名和邮箱是否存在
     */
    private void checkUsernameAndEmailExist(String username, String email, String enableRepeatRegister) {
        if (StrUtil.isEmpty(username) || StrUtil.isEmpty(email)) {
            throw new CustomException("用户名或者邮箱不能为空");
        }
        // 验证邮箱格式
        if (!email.matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$")) {
            throw new CustomException("邮箱格式不正确");
        }
        // 验证用户名格式（假设只允许字母、数字、下划线，长度3-20）
        if (!username.matches("^[a-zA-Z0-9_]{3,20}$")) {
            throw new CustomException("用户名格式不正确，仅允许字母、数字和下划线，长度3-20");
        }
        // 2. 检查用户名和邮箱是否重复
        ChatGptUserEntity existingUser = chatGptUserMapper.selectOneByNameOrEmail(username, email);
        if (existingUser != null) {
            if (username.equalsIgnoreCase(existingUser.getUserToken())) {
                throw new CustomException("用户名已存在");
            }
            if (email.equals(existingUser.getEmail())) {
                throw new CustomException("邮箱已存在");
            }
        }
        if ("true".equals(enableRepeatRegister)) {
            // 判断用户是否是重复注册的
            checkRepeatRegister(username, email);
        }
    }

    private void checkRepeatRegister(String username, String email) {
        String clientIp = getClientIp(request);
        String deviceId = request.getHeader("device-id");
        if (!StringUtils.hasText(clientIp) && !StringUtils.hasText(deviceId)) {
            log.warn("重复注册检查失败：clientIp和deviceId均为空, username: {}, email: {}", username, email);
            throw new CustomException("无法验证注册请求，请提供有效的客户端信息");
        }
        // 3. 数据库查询，检查是否近期有相同IP或设备ID的注册记录
        LambdaQueryWrapper<ChatGptUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(ChatGptUserEntity::getDeletedAt)
                .and(w -> {
                    if (StringUtils.hasText(clientIp)) {
                        w.eq(ChatGptUserEntity::getClientIp, clientIp).or();
                    }
                    if (StringUtils.hasText(deviceId)) {
                        w.eq(ChatGptUserEntity::getDeviceId, deviceId);
                    }
                });

        long count = chatGptUserMapper.selectCount(wrapper);
        if (count > 0) {
            log.warn("重复注册请求，clientIp: {}, deviceId: {}, username: {}, email: {}",
                    clientIp, deviceId, username, email);
            if (StringUtils.hasText(clientIp) && StringUtils.hasText(deviceId)) {
                throw new CustomException("检测到重复注册：IP或设备ID已被使用");
            } else if (StringUtils.hasText(clientIp)) {
                throw new CustomException("检测到重复注册：IP已被使用");
            } else {
                throw new CustomException("检测到重复注册：设备ID已被使用");
            }
        }
    }

    private void validRegisterCode(String email, String authCode) {
        // 先认证验证码
        String code = stringRedisTemplate.opsForValue().get(VERIFICATION_CODE_PREFIX + email);
        if (StrUtil.isBlank(code)) {
            throw new CustomException("验证码过期，请重新获取");
        }

        if (!Objects.equals(authCode, code)) {
            throw new CustomException("验证码有误，请重新输入");
        }
    }

    private ChatGptUserEntity updateInviterExpire(RegisterDto registerDto, String affTime, String enablePlus, String enableInviteCode) {
        if ("true".equals(enableInviteCode) && StrUtil.isEmpty(registerDto.getInviter())) {
            throw new CustomException("管理员开启了邀请码注册，需要使用正确的邀请码才可以注册。");
        }
        if (StrUtil.isNotBlank(registerDto.getInviter())) {
            ChatGptUserEntity inviter = getUserByInviter(registerDto.getInviter());
            if (ObjectUtil.isNotEmpty(inviter)) {
                inviter.setExpireTime(inviter.getExpireTime().plusHours(Long.parseLong(StrUtil.isEmpty(affTime) ? "0" : affTime)));
                if (Objects.equals(enablePlus, "true")) {
                    // 如果开启注册可用plus，则用户邀请的时候赠送的也是plus时长
                    LocalDateTime plusExpireTime = inviter.getPlusExpireTime();
                    // 如果用户的plus过期时间为空或者已经过期，则使用当前时间
                    if (ObjectUtil.isEmpty(plusExpireTime) || plusExpireTime.isBefore(LocalDateTime.now())) {
                        plusExpireTime = LocalDateTime.now();
                    }
                    inviter.setPlusExpireTime(plusExpireTime.plusHours(Long.parseLong(StrUtil.isEmpty(affTime) ? "0" : affTime)));
                }
                chatGptUserMapper.updateByIdWithoutTenant(inviter);
                return inviter;
            } else {
                throw new CustomException("邀请码不存在");
            }
        }
        return null;
    }

    /**
     * 根据邀请码获取用户信息
     * @param inviter 邀请码
     * @return 用户信息
     */
    private ChatGptUserEntity getUserByInviter(String inviter) {
        return this.lambdaQuery().eq(ChatGptUserEntity::getAffCode, inviter).one();
    }

    private ChatGptUserEntity saveNewUser(RegisterDto registerDto,
                             String freeTime,
                             String enablePlus,
                             ChatGptUserEntity inviter,
                             Double affRate,
                             String modelLimits) {
        LocalDateTime localDateTime = LocalDateTime.now().plusHours(Long.parseLong(StrUtil.isEmpty(freeTime) ? "0" : freeTime));
        ChatGptUserEntity user = new ChatGptUserEntity();
        user.setUserToken(registerDto.getUsername());
        user.setEmail(registerDto.getEmail());
        user.setExpireTime(localDateTime);
        user.setPlusExpireTime(Objects.equals(enablePlus, "true") ? localDateTime :  null);
        user.setIsAdmin(0);
        user.setAffCode(RandomUtil.randomStringUpper(6)); // 新增邀请码
        user.setPassword(DigestUtils.md5DigestAsHex(registerDto.getPassword().getBytes()));
        user.setInviterId(ObjectUtil.isEmpty(inviter) ? 0 : inviter.getId()); // 设置邀请人id
        user.setClientIp(getClientIp(request));
        user.setDeviceId(request.getHeader("device-id"));
        user.setAffRate(affRate);
        user.setLastActiveTime(LocalDateTime.now());
        user.setModelLimits(getSysModelLimitMap(modelLimits));
        user.setLoginToken(IdUtil.simpleUUID()); // 生成认证token
        user.setTenantId(sysTenantService.getTenantIdByHost(getHost()));
        save(user);
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> login(LoginDto loginDto) {
        String username = loginDto.getUsername();
        String key = LOGIN_ATTEMPTS + username;
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(LOGIN_LOCK + username))){
            throw new CustomException("密码错误次数超过5次，已被锁定，请联系管理员处理或者"+ LOCK_TIME +"分钟后重试。");
        }
        // 获取当前失败次数
        int attempts = Integer.parseInt(StrUtil.isEmpty(stringRedisTemplate.opsForValue().get(key))? "0" :stringRedisTemplate.opsForValue().get(key));
        ChatGptUserEntity token = chatGptUserMapper.selectOneByNameAndLoginType(username, LoginType.ACCOUNT.getType());
        if (ObjectUtil.isEmpty(token)) {
            throw new CustomException("用户名或邮箱错误");
        }
        if (!ObjectUtil.equal(DigestUtils.md5DigestAsHex(loginDto.getPassword().getBytes()), token.getPassword())) {
            attempts++;
            // 记录失败次数
            stringRedisTemplate.opsForValue().set(key, String.valueOf(attempts), LOCK_TIME, TimeUnit.MINUTES);
            if (attempts >= MAX_ATTEMPTS) {
                stringRedisTemplate.opsForValue().set(LOGIN_LOCK + username, "LOCKED", LOCK_TIME, TimeUnit.MINUTES);
                throw new CustomException("账户已锁定，请 " + LOCK_TIME + " 分钟后再试");
            }
            throw new CustomException("用户名或密码错误，剩余尝试次数:" + (MAX_ATTEMPTS - attempts));
        } else {
            stringRedisTemplate.delete(key);
            stringRedisTemplate.delete(LOGIN_LOCK + username);
        }
        if (token.getStatus() == 0) {
            throw new CustomException("当前账号已被封禁，请联系管理员");
        }
        // 判断用户是否有套餐id并且是首次登录，则权益开始生效。
        Integer subTypeId = token.getSubTypeId();
        if (subTypeId > 0 && ObjectUtil.isEmpty(token.getLastActiveTime())) {
            log.info("批量生成的用户首次登录，权益开始生效");
            ChatGptSubTypeEntity subType = chatGptSubTypeMapper.getSubtypeByIdWithoutTenant(String.valueOf(subTypeId));
            calculateExpirationTime(subType, token);
            // 根据套餐更新模型速率信息
            token.setModelLimits(subType.getModelLimits());
        }

        // 生成jwt
        String jwtToken = JwtUtil.create(token.getId(), token.getUserToken(), token.getIsAdmin());

        // 记录用户token
        String userTokenKey = "user_tokens:" + token.getUserToken();
        stringRedisTemplate.opsForValue().set(userTokenKey, "valid");

        String clientIp = getClientIp(request);
        token.setLastActiveTime(LocalDateTime.now());
        token.setClientIp(clientIp);
        this.updateByIdWithoutTenant(token);

        // 设置返回信息
        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtil.copyProperties(token, userInfoVo);
        return Map.of("token", jwtToken, "userInfo", userInfoVo);
    }

    @Override
    public void resetPassword(ResetPasswordDto resetPasswordDto) {
        String email = resetPasswordDto.getEmail();
        // 先认证验证码
        validRegisterCode(email, resetPasswordDto.getCode());
        // 校验用户信息
        checkUserInfo(resetPasswordDto);
        // 更新密码
        updatePassword(resetPasswordDto);
        // 验证成功则删除缓存
        String username = resetPasswordDto.getUsername();
        stringRedisTemplate.delete(LOGIN_LOCK + username);
        stringRedisTemplate.delete(LOGIN_ATTEMPTS + username);
        stringRedisTemplate.delete(VERIFICATION_CODE_PREFIX + email);
    }

    @Override
    public Map<String, Object> getCarList(String type, Integer page, Integer size) {
        String baseUrl = IpUtils.getRefererDomain(request);
        List<ChatGptSessionEntity> carList = getChatGptSessionEntities(type, page, size);
        List<String> statusOrder = Arrays.asList("空闲", "繁忙", "停运", "翻车");
        if (CollectionUtils.isNotEmpty(carList)) {
            List<CarInfoVo> newCarList = carList.stream().map(e -> {
                // 从缓存中获取数据，若不存在则加载
                CarStatus carStatus = carStatusCache.get(e.getCarID(), id -> fetchCarInfo(baseUrl, id, e));
                if (carStatus != null && Objects.equals(carStatus.getAccountReady(), "true")) {
                    CarInfoVo carInfoVo = convertToCarInfoVo(e.getCarID(), e.getIsPlus(), carStatus, true);
                    carInfoVo.setCarStatus(carStatus);
                    return carInfoVo;
                }
                return null;
            }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());

            // 添加虚拟数据
            if ("plus".equals(type) && needVirtual) {
                Map<String, String> keyVlaueMap = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("virtualNo", "virtualNameList",
                        "virtualTeamNo", "virtualTeamNameList", "virtualProNo", "virtualProNameList"));
                if (carList.stream().anyMatch(e -> PLAN_TYPE_PLUS.equals(e.getIsPlus()))) {
                    addVirtualCars(newCarList, status, keyVlaueMap.get("virtualNo"), PLAN_TYPE_PLUS, keyVlaueMap.get("virtualNameList"), cachedPlusCarList);
                }
                if (carList.stream().anyMatch(e -> PLAN_TYPE_TEAM.equals(e.getIsPlus()))) {
                    addVirtualCars(newCarList, status, keyVlaueMap.get("virtualTeamNo"), PLAN_TYPE_TEAM, keyVlaueMap.get("virtualTeamNameList"), cachedTeamCarList);
                }
                if (carList.stream().anyMatch(e -> PLAN_TYPE_PRO.equals(e.getIsPlus()))) {
                    addVirtualCars(newCarList, status, keyVlaueMap.get("virtualProNo"), PLAN_TYPE_PRO, keyVlaueMap.get("virtualProNameList"), cachedProCarList);
                }
            }

            // 按自定义排序
            newCarList = newCarList.stream()
                    .sorted(Comparator.comparing((CarInfoVo::getIsPlus))
                            .reversed()
                            .thenComparing(e -> statusOrder.indexOf(e.getStatus()))
                            .thenComparingInt(e -> Optional.ofNullable(e.getCarStatus())
                                    .map(CarStatus::getCount)
                                    .orElse(0))) // 根据 count 进行次级排序
                    .toList();
            Map<String, Object> map = new HashMap<>();
            map.put("carList", newCarList);
            return map;
        }
        return null;
    }

    // 辅助方法：从远程获取数据，并处理为缓存格式
    private CarStatus fetchCarInfo(String baseUrl, String carID, ChatGptSessionEntity entity) {
        try {
            // 获取车的状态
            String statusUrl = String.format("%s/status?carid=%s", baseUrl, carID);
            log.debug("正在获取GPT车号 [{}] 状态，URL: {}", carID, statusUrl);

            CarStatus carStatus = restTemplate.getForObject(statusUrl, CarStatus.class);
            entity.setCount(Optional.ofNullable(carStatus)
                    .map(CarStatus::getCount)
                    .map(Long::valueOf)
                    .orElse(0L));

            log.debug("成功获取GPT车号 [{}] 状态: {}", carID, carStatus != null ? carStatus.getAccountReady() : "null");
            return carStatus;
        } catch (org.springframework.web.client.ResourceAccessException e) {
            // 网络超时或连接失败
            log.warn("获取GPT车号 [{}] 状态超时或连接失败: {}", carID, e.getMessage());
            entity.setCount(0L);
        } catch (org.springframework.web.client.HttpClientErrorException e) {
            // HTTP 4xx 错误
            log.warn("获取GPT车号 [{}] 状态失败，客户端错误: {} - {}", carID, e.getStatusCode(), e.getMessage());
            entity.setCount(0L);
        } catch (org.springframework.web.client.HttpServerErrorException e) {
            // HTTP 5xx 错误
            log.warn("获取GPT车号 [{}] 状态失败，服务器错误: {} - {}", carID, e.getStatusCode(), e.getMessage());
            entity.setCount(0L);
        } catch (Exception e) {
            log.error("获取GPT车号 [{}] 状态时发生未知错误", carID, e);
            entity.setCount(0L);
        }
        return null;
    }

    // 辅助方法：添加虚拟车辆
    private void addVirtualCars(List<CarInfoVo> carList, List<String> status, String virtualNo, Integer type, String virtualNameList, List<CarInfoVo> cachedCarList) {
        if (StrUtil.isNotEmpty(virtualNo) && Long.parseLong(virtualNo) > 0) {
            long num = Long.parseLong(virtualNo);
            // 如果缓存中已经有车号列表并且与设置的虚拟数量一致，直接返回
            if (cachedCarList.size() == num) {
                carList.addAll(cachedCarList);
                return;
            }
            cachedCarList.clear();  // 如果虚拟数量不同，清空缓存列表
            Random random = new Random();
            for (int i = 0; i < num; i++) {
                CarInfoVo carInfoVo = new CarInfoVo();
                carInfoVo.setCarID(getVirtualCarName(i, virtualNameList));
                carInfoVo.setIsPlus(type);
                CarStatus carStatus = new CarStatus();
                carStatus.setIsPlus(String.valueOf(type));
                String randomStatus = isFreeTime() ? status.get(0) : status.get(random.nextInt(status.size()));
                int count;
                if ("空闲".equals(randomStatus)) {
                    count = random.nextInt(60); // 空闲状态下 count 范围是 0 到 19
                } else {
                    count = 60 + random.nextInt(60); // 繁忙状态下 count 范围是 20 到 39
                }
                // 凌晨清空虚拟车的次数
                if (isFreeTime()) {
                    count = 0;
                }
                carStatus.setCount(count);
                carInfoVo.setCount((long) count);
                carInfoVo.setStatus(randomStatus);
                carInfoVo.setDetail("空闲".equals(randomStatus)? "推荐": "可用");
                carInfoVo.setCarStatus(carStatus);
                carList.add(carInfoVo);
                cachedCarList.add(carInfoVo);
            }
        }
    }

    /**
     * 生成虚拟车的名称
     * 如果用户自定义了虚拟车的名称且虚拟车名称数与节点数量一致，则使用自定义的，超出的虚拟车使用随机的名称。
     * 未定义虚拟车节点名称则使用随机的名称
     * @param num 当前车
     * @return 虚拟车名称
     */
    private String getVirtualCarName(int num, String virtualNameList) {
        if (StrUtil.isEmpty(virtualNameList)) {
            return CarIdUtils.generatorCarID();
        }

        String[] carNames = virtualNameList.split(",");
        return (num >= 0 && num < carNames.length) ? carNames[num] : CarIdUtils.generatorCarID();
    }

    private boolean isFreeTime() {
        LocalDateTime now = LocalDateTime.now();
        // 将时间范围定义为 LocalTime 对象
        LocalTime start = LocalTime.of(0, 0); // 凌晨 12 点
        LocalTime end = LocalTime.of(8, 0);   // 早上 8 点
        // 提取当前时间的 LocalTime 部分
        LocalTime currentTime = now.toLocalTime();
        return currentTime.isAfter(start) && currentTime.isBefore(end);
    }


    /**
     * 根据类型获取节点数据
     * @param type 账号类型
     * @return 实体集合
     */
    private List<ChatGptSessionEntity> getChatGptSessionEntities(String type, Integer page, Integer size) {
        IPage<ChatGptSessionEntity> pageInfo = new Page<>(page, size);
        List<ChatGptSessionEntity> carList = null;
        LambdaQueryWrapper<ChatGptSessionEntity> bindWrapper = new LambdaQueryWrapper<>();
        bindWrapper.eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS);
        bindWrapper.isNull(ChatGptSessionEntity::getDeletedAt);
        bindWrapper.orderByAsc(ChatGptSessionEntity::getCount);

        String token = request.getHeader(AUTH_HEADER);
        // 如果用户已登录,根据用户是否有独享车，如果有独享车就显示独显车，否则就获取未绑定的车号
        if (StrUtil.isNotEmpty(token)) {
            Long uid = JwtUtil.getUid(token);
            if (ObjectUtils.isNotEmpty(uid)) {
                bindWrapper.eq(ChatGptSessionEntity::getUserId, uid);
                bindWrapper.ge(ChatGptSessionEntity::getExclusiveExpireTime, LocalDateTime.now());
                List<ChatGptSessionEntity> gptSessionEntities = chatGptSessionMapper.selectList(pageInfo, bindWrapper);
                // 判断一下当前的查询结果与type是否相符
                if (!CollectionUtils.isEmpty(gptSessionEntities)) {
                    List<ChatGptSessionEntity> collect = gptSessionEntities.stream()
                            .filter(e -> comparePlanType(e, type)).toList();
                    if (!CollectionUtils.isEmpty(collect)) {
                        // 不需要虚拟车
                        needVirtual = false;
                        return collect;
                    }
                }
            }
        }
        LambdaQueryWrapper<ChatGptSessionEntity> wrapper = new LambdaQueryWrapper<>();
        // 添加绑定时间已经过期的查询条件
        wrapper.and(w -> w.lt(ChatGptSessionEntity::getExclusiveExpireTime, LocalDateTime.now())  // 时间已过期
                .or().isNull(ChatGptSessionEntity::getExclusiveExpireTime)); // 或者时间为空
        wrapper.eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS);
        wrapper.isNull(ChatGptSessionEntity::getDeletedAt);
        wrapper.orderByAsc(ChatGptSessionEntity::getCount);
        switch(type) {
            case "free":
                String freeNodes = chatGptConfigService.getValueByKey("freeNodes");
                if (StrUtil.isNotBlank(freeNodes) && !freeNodes.equals("0")) {
                    wrapper.eq(ChatGptSessionEntity::getIsPlus, PLAN_TYPE_FREE);
                    wrapper.last ("limit " + freeNodes);
                    carList = chatGptSessionMapper.selectList(wrapper);
                }
                break;
            case "4o":
                wrapper.eq(ChatGptSessionEntity::getIsPlus, PLAN_TYPE_FREE);
                carList = chatGptSessionMapper.selectList(pageInfo, wrapper);
                break;
            case "plus":
                needVirtual = true;
                wrapper.in(ChatGptSessionEntity::getIsPlus, PLAN_TYPE_PLUS, PLAN_TYPE_TEAM, PLAN_TYPE_PRO);
                carList = chatGptSessionMapper.selectList(pageInfo, wrapper);
                // 异步更新session 的订阅类型
                asyncUpdateSessionType(carList);
                break;
            default:
                throw new CustomException("请先到后台【系统配置】中配置-》【节点名称】");
        }
        return carList;
    }

    private boolean comparePlanType(ChatGptSessionEntity e, String type) {
        return switch (type) {
            case "free", "4o" -> Objects.equals(e.getIsPlus(), PLAN_TYPE_FREE);
            case "plus" -> Objects.equals(e.getIsPlus(), PLAN_TYPE_PLUS) ||
                    Objects.equals(e.getIsPlus(), PLAN_TYPE_TEAM) ||
                    Objects.equals(e.getIsPlus(), PLAN_TYPE_PRO);
            default -> throw new CustomException("未知类型：" + type);
        };
    }

    /**
     * 异步更新gpt session的订阅类型
     * @param carList
     */
    private void asyncUpdateSessionType(List<ChatGptSessionEntity> carList) {
        carList.forEach(e -> {
            if (e.getOfficialSession().contains("\"plan_type\":\"team\"")) {
                e.setIsPlus(2); // 更新为 team 类型
            } else if (e.getOfficialSession().contains("\"plan_type\":\"pro\"")) {
                e.setIsPlus(3); // 更新为 pro 类型
            }
        });

        // 过滤出需要更新的数据（如果需要批量更新时过滤掉无变化的对象）
        List<ChatGptSessionEntity> todoList = carList.stream()
                .filter(e -> e.getIsPlus() == 2 || e.getIsPlus() == 3) // 确保只包含更新的对象
                .toList();

        // 执行批量更新
        if (CollectionUtils.isNotEmpty(todoList)) {
            CompletableFuture.runAsync(() -> chatGptSessionMapper.insertOrUpdate(todoList), commonAsyncExecutor);
        }
    }


    /**
     * 设置用户过期时间
     * @param subType 订阅套餐
     * @param user 用户信息
     */
    @Override
    public void calculateExpirationTime(ChatGptSubTypeEntity subType, ChatGptUserEntity user) {
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户不存在");
        }
        if (ObjectUtil.isEmpty(subType)) {
            throw new CustomException("订阅信息不存在");
        }
        LocalDateTime now = LocalDateTime.now();
        // 套餐类型
        SubscriptionType type = SubscriptionType.fromType(subType.getSubType());
        switch (type) {
            case GPT_ONLY:
                handleChatGptOrPlus(user, subType);
                break;
            case CLAUDE_ONLY:
                handleClaudeOrPro(user, subType);
                break;
            case GPT_AND_CLAUDE:
                handleChatGptOrPlus(user, subType);
                handleClaudeOrPro(user, subType);
                break;
            case GROK_ONLY:
                handleGrokOrSuper(user, subType);
                break;
            case GPT_AND_GROK:
                handleChatGptOrPlus(user, subType);
                handleGrokOrSuper(user, subType);
                break;
            case CLAUDE_AND_GROK:
                handleClaudeOrPro(user, subType);
                handleGrokOrSuper(user, subType);
                break;
            case GPT_CLAUDE_AND_GROK:
                handleChatGptOrPlus(user, subType);
                handleClaudeOrPro(user, subType);
                handleGrokOrSuper(user, subType);
                break;
            case DRAW:
                // 更新用户的绘画额度
                drawingQuotaService.updateUserDrawQuota(user.getId(), subType.getDrawQuota(), subType.getValidDays());
                // 新增会话额度变更记录
                changeRecordService.addQuotaChangeRecord(user.getId(), QuotaChangeType.PURCHASE.getCode(), subType.getDrawQuota(), "变更说明：购买" + subType.getDrawQuota() +"次绘画额度");
                break;
            default:
                throw new CustomException("未知的套餐类型：" + type);
        }
        // 设置为付费用户
        user.setUserType(USER_TYPE_ADVANCE);
        user.setUpdateTime(now);
        user.setSubTypeId(Math.toIntExact(subType.getId()));
        // 画图套餐不更新速率
        log.info("套餐类型：{}", subType.getSubType());
        if (!Objects.equals(subType.getSubType(), SubscriptionType.DRAW.getType())) {
            user.setModelLimits(dealUserModelLimit(user, subType));
        }
    }

    private void handleGrokOrSuper(ChatGptUserEntity user, ChatGptSubTypeEntity subType) {
        Integer isSuper = subType.getIsSuper();
        Integer validDays = subType.getValidDays();
        if (Objects.equals(isSuper, PLAN_TYPE_GROK_SUPER)) {
            calcNormalGrokExpireTime(user, validDays);
            calcNormalGrokSuperExpireTime(user, validDays);
        } else {
            calcNormalGrokExpireTime(user, validDays);
        }
    }

    private void calcNormalGrokSuperExpireTime(ChatGptUserEntity user, Integer validDays) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime superExpireTime = user.getGrokSuperExpireTime();
        // 如果用户的plus未过期，还需要判断普通套餐是否过期
        if (ObjectUtil.isNotEmpty(superExpireTime) && superExpireTime.isAfter(now)) {
            user.setGrokSuperExpireTime(superExpireTime.plusDays(validDays));
        }else{
            user.setGrokSuperExpireTime(now.plusDays(validDays));
        }
    }

    private void calcNormalGrokExpireTime(ChatGptUserEntity user, int validDays) {
        LocalDateTime grokExpireTime = user.getGrokExpireTime();

        LocalDateTime now = LocalDateTime.now();
        // 如果用户的pro未过期
        if (ObjectUtil.isNotEmpty(grokExpireTime) && grokExpireTime.isAfter(now)) {
            user.setGrokExpireTime(grokExpireTime.plusDays(validDays));
        }else{
            // 已过期
            user.setGrokExpireTime(now.plusDays(validDays));
        }

    }

    private Map<String, ModelLimitDto> dealUserModelLimit(ChatGptUserEntity user, ChatGptSubTypeEntity subType) {
        Map<String, ModelLimitDto> modelLimits = user.getModelLimits();
        Map<String, ModelLimitDto> subTypeModelLimits = subType.getModelLimits();
        if (ObjectUtil.isEmpty(modelLimits)) {
            modelLimits = new HashMap<>();
        }
        updateModelLimit(modelLimits, subTypeModelLimits);
        return modelLimits;
    }

    /**
     * 使用套餐速率来新增、更新用户的模型速率
     * @param userModelLimits 用户模型速率
     * @param subTypeModelLimits 套餐模型速率
     */
    private void updateModelLimit(Map<String, ModelLimitDto> userModelLimits, Map<String, ModelLimitDto> subTypeModelLimits) {
        if (userModelLimits == null || subTypeModelLimits == null) {
            return;
        }
        // 遍历套餐模型数据
        Set<String> keys = subTypeModelLimits.keySet();
        for (String key : keys) {
            ModelLimitDto modelLimitDto = subTypeModelLimits.get(key);
            // 存在于用户速率中则更新
            if (userModelLimits.containsKey(key)) {
                ModelLimitDto userModelLimit = userModelLimits.get(key);
                userModelLimit.setLimit(modelLimitDto.getLimit());
                userModelLimit.setPer(modelLimitDto.getPer());
            } else {
                userModelLimits.put(key, modelLimitDto);
            }
        }
    }

    /**
     * 计算用户的pro过期时间
     * @param user
     * @param validDays
     */
    private static void calcClaudeProExpireTime(ChatGptUserEntity user, int validDays) {
        LocalDateTime now = LocalDateTime.now();
        // 如果用户的pro未过期
        LocalDateTime proExpireTime = user.getClaudeProExpireTime();
        if (ObjectUtil.isNotEmpty(proExpireTime) && proExpireTime.isAfter(now)) {
            user.setClaudeProExpireTime(proExpireTime.plusDays(validDays));
        }else{
            // 已过期
            user.setClaudeProExpireTime(now.plusDays(validDays));
        }
    }

    /**
     * 处理chatgpt套餐情况
     * @param user
     * @param subType
     */
    private void handleChatGptOrPlus(ChatGptUserEntity user, ChatGptSubTypeEntity subType) {
        LocalDateTime expireTime = user.getExpireTime();
        int validDays = subType.getValidDays();
        LocalDateTime now = LocalDateTime.now();
        if (PLAN_TYPE_PLUS.equals(subType.getIsPlus())) {
            calcPlusExpireTime(user, now, validDays, expireTime);
            calcNormalExpireTime(user, expireTime, now, validDays);
        } else {
            calcNormalExpireTime(user, expireTime, now, validDays);
        }
    }

    /**
     * 计算claude过期时间
     * @param user
     * @param subType
     */
    private void handleClaudeOrPro(ChatGptUserEntity user, ChatGptSubTypeEntity subType) {
        int validDays = subType.getValidDays();
        if (PLAN_TYPE_CLAUDE_PRO.equals(subType.getIsPro())) {
            calcClaudeProExpireTime(user, validDays);
            calcClaudeNormalExpireTime(user, validDays);
        } else {
            calcClaudeNormalExpireTime(user, validDays);
        }
        user.setIsPlus(ENABLE_CLAUDE_ACCESS);
    }

    /**
     * 更新用户普通claude权益过期时间
     * @param user
     * @param validDays
     */
    private static void calcClaudeNormalExpireTime(ChatGptUserEntity user, int validDays) {
        // 如果是普通套餐，判断普通套餐是否过期，未过期，套餐时间为在未过期的时间上加套餐天数
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime claudeExpireTime = user.getClaudeExpireTime();
        if (ObjectUtil.isNotEmpty(claudeExpireTime) && claudeExpireTime.isAfter(now)) {
            user.setClaudeExpireTime(claudeExpireTime.plusDays(validDays));
        } else {
            // 已过期的话，套餐时间为当前时间上加套餐天数
            user.setClaudeExpireTime(now.plusDays(validDays));
        }
    }

    /**
     * 计算普通会员过期时间
     * @param user
     * @param expireTime
     * @param currentTime
     * @param validDays
     */
    private static void calcNormalExpireTime(ChatGptUserEntity user, LocalDateTime expireTime, LocalDateTime currentTime, int validDays) {
        // 如果是普通套餐，判断普通套餐是否过期，未过期，套餐时间为在未过期的时间上加套餐天数
        if (ObjectUtil.isNotEmpty(expireTime) && expireTime.isAfter(currentTime)) {
            user.setExpireTime(expireTime.plusDays(validDays));
        } else {
            // 已过期的话，套餐时间为当前时间上加套餐天数
            user.setExpireTime(currentTime.plusDays(validDays));
        }
    }

    /**
     * 计算plus过期时间
     * @param user
     * @param currentTime
     * @param validDays
     * @param expireTime
     */
    private static void calcPlusExpireTime(ChatGptUserEntity user, LocalDateTime currentTime, int validDays, LocalDateTime expireTime) {
        LocalDateTime plusExpireTime = user.getPlusExpireTime();
        // 如果用户的plus未过期，还需要判断普通套餐是否过期
        if (ObjectUtil.isNotEmpty(plusExpireTime) && plusExpireTime.isAfter(currentTime)) {
            user.setPlusExpireTime(plusExpireTime.plusDays(validDays));
        }else{
            user.setPlusExpireTime(currentTime.plusDays(validDays));
        }
        // plus开启claude权限，非plus不改变状态
        user.setIsPlus(ENABLE_CLAUDE_ACCESS);
    }

    /**
     * 检查用户信息
     * @param resetPasswordDto 重置密码dto
     */
    private void checkUserInfo(ResetPasswordDto resetPasswordDto) {
        ChatGptUserEntity user = chatGptUserMapper.selectOneByNameOrEmail(resetPasswordDto.getUsername(),
                resetPasswordDto.getEmail());
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户信息错误，请检查");
        }
        if (user.getUserType() == 2) {
            throw new CustomException("免费用户不允许修改密码");
        }
    }

    /**
     * 更新用户密码
     * @param resetPasswordDto 重置密码dto
     */
    private void updatePassword(ResetPasswordDto resetPasswordDto) {
        UpdateWrapper<ChatGptUserEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("email", resetPasswordDto.getEmail());
        wrapper.eq("userToken", resetPasswordDto.getUsername());
        wrapper.set("password", DigestUtils.md5DigestAsHex(resetPasswordDto.getNewPassword().getBytes()));
        chatGptUserMapper.update(wrapper);
    }
}
